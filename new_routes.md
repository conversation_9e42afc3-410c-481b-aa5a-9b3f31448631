# Complete Nepali App API Documentation - cURL Only

**Base URL:** `http://localhost:8204`  
**Authentication:** JWT <PERSON>er <PERSON> Required  
**Database:** `test_nepali_app`

## Authentication

All endpoints require JWT Bearer token in the Authorization header:
```
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

---

## 1. GET ALL THEMES

**Purpose:** Retrieve all Nepal-themed categories with filtering and pagination

**URL:** `GET /v1/management/curated/themes`

**Query Parameters:**
- `page` (int, default: 1) - Page number
- `limit` (int, default: 50, max: 100) - Items per page  
- `search` (string) - Search in theme names/descriptions
- `category` (string) - Filter by category (संस्कृति, भूगोल, इतिहास, etc.)
- `is_active` (boolean) - Filter by active status
- `start_date` (string) - Start date filter (YYYY-MM-DD)
- `end_date` (string) - End date filter (YYYY-MM-DD)
- `sort_order` (string) - Sort order: "asc" or "desc"

**cURL Example:**
```bash
curl -X 'GET' \
  'http://localhost:8204/v1/management/curated/themes?page=1&limit=10&search=संस्कृति&category=संस्कृति&is_active=true' \
  -H 'accept: application/json' \
  -H 'Authorization: Bearer <your_jwt_token>'
```

**Response (200 OK):**
```json
{
  "data": [
    {
      "_id": "685e2955f928ae494af5e969",
      "name": "नेपाली संस्कृति र परम्परा",
      "name_en": "Nepali Culture and Traditions",
      "description": "नेपालको समृद्ध सांस्कृतिक सम्पदा र परम्पराहरूको बारेमा जान्नुहोस्",
      "description_en": "Learn about Nepal's rich cultural heritage and traditions",
      "category": "संस्कृति",
      "category_en": "Culture",
      "icon": "🏛️",
      "color": "#FF6B6B",
      "is_active": true,
      "created_at": "2025-06-27T09:51:42.937Z",
      "updated_at": "2025-06-27T09:51:42.937Z"
    }
  ],
  "meta": {
    "page": 1,
    "limit": 10,
    "total": 30,
    "total_pages": 3
  }
}
```

---

## 2. GET CONTENT SETS BY THEME

**Purpose:** Get all content sets for a specific theme

**URL:** `GET /v1/management/curated/themes/{theme_id}`

**Path Parameters:**
- `theme_id` (string) - Theme ObjectId

**Query Parameters:**
- `page` (int, default: 1) - Page number
- `limit` (int, default: 20, max: 100) - Items per page
- `difficulty_level` (int, 1-3) - Filter by difficulty (1=easy, 2=medium, 3=hard)
- `status` (string) - Filter by status (pending, completed, etc.)
- `gentype` (string) - Filter by generation type (primary, follow_up, etc.)

**cURL Example:**
```bash
curl -X 'GET' \
  'http://localhost:8204/v1/management/curated/themes/685e2955f928ae494af5e969?page=1&limit=5&difficulty_level=1' \
  -H 'accept: application/json' \
  -H 'Authorization: Bearer <your_jwt_token>'
```

**Response (200 OK):**
```json
{
  "data": [
    {
      "_id": "685e6d3b15fd9122a46a32b3",
      "title": "नेपाली संस्कृति र परम्परा - सेट 1",
      "title_en": "Nepali Culture and Traditions - Set 1",
      "theme_id": "685e2955f928ae494af5e969",
      "difficulty_level": 1,
      "total_tasks": 6,
      "total_score": 60,
      "tasks": [
        "685e6d3b15fd9122a46a32b4",
        "685e6d3b15fd9122a46a32b5",
        "685e6d3b15fd9122a46a32b6"
      ],
      "status": "active",
      "created_at": "2025-06-27T09:51:42.937Z"
    }
  ],
  "meta": {
    "page": 1,
    "limit": 5,
    "total": 20,
    "total_pages": 4
  }
}
```

---

## 3. GET CONTENT SET DETAILS

**Purpose:** Get detailed information about a specific content set

**URL:** `GET /v1/management/curated/content-sets/{content_set_id}`

**Path Parameters:**
- `content_set_id` (string) - Content set ObjectId

**cURL Example:**
```bash
curl -X 'GET' \
  'http://localhost:8204/v1/management/curated/content-sets/685e6d3b15fd9122a46a32b3' \
  -H 'accept: application/json' \
  -H 'Authorization: Bearer <your_jwt_token>'
```

**Response (200 OK):**
```json
{
  "data": {
    "_id": "685e6d3b15fd9122a46a32b3",
    "title": "नेपाली संस्कृति र परम्परा - सेट 1",
    "title_en": "Nepali Culture and Traditions - Set 1",
    "description": "नेपाली संस्कृतिका आधारभूत कुराहरू",
    "description_en": "Basic aspects of Nepali culture",
    "theme_id": "685e2955f928ae494af5e969",
    "difficulty_level": 1,
    "total_tasks": 6,
    "total_score": 60,
    "tasks": [
      "685e6d3b15fd9122a46a32b4",
      "685e6d3b15fd9122a46a32b5"
    ],
    "user_id": "68391d86b8b0e7ec9ababfbb",
    "created_at": "2025-06-27T09:51:42.937Z"
  }
}
```

---

## 4. GET QUESTIONS FOR CONTENT SET

**Purpose:** Get all questions/tasks for a specific content set

**URL:** `GET /v1/management/curated/questions/{content_set_id}`

**Path Parameters:**
- `content_set_id` (string) - Content set ObjectId

**cURL Example:**
```bash
curl -X 'GET' \
  'http://localhost:8204/v1/management/curated/questions/685e6d3b15fd9122a46a32b3' \
  -H 'accept: application/json' \
  -H 'Authorization: Bearer <your_jwt_token>'
```

**Response (200 OK):**
```json
[
  {
    "question": {
      "text": "नेपालको राष्ट्रिय फूल कुन हो?",
      "translated_text": "What is the national flower of Nepal?",
      "type": "single_choice"
    }
  },
  {
    "question": {
      "text": "काठमाडौं उपत्यकामा कुन ऐतिहासिक स्थलहरू छन्?",
      "translated_text": "Which historical sites are in Kathmandu Valley?",
      "type": "multiple_choice"
    }
  }
]
```

---

## 5. CONVERT CURATED CONTENT TO TASK SET

**Purpose:** Convert a curated content set to a playable task set for the user

**URL:** `POST /v1/management/curated/convert/{curated_content_set_id}`

**Path Parameters:**
- `curated_content_set_id` (string) - Curated content set ObjectId

**cURL Example:**
```bash
curl -X 'POST' \
  'http://localhost:8204/v1/management/curated/convert/685e6d3b15fd9122a46a32b3' \
  -H 'accept: application/json' \
  -H 'Authorization: Bearer <your_jwt_token>' \
  -H 'Content-Type: application/json'
```

**Response (200 OK):**
```json
{
  "task_set_id": "675a1b2c3d4e5f6789abcdef"
}
```

**What this does:**
- Creates a new task set in the `task_sets` collection
- Copies all curated content items to `task_items` collection with new IDs
- Links task items to the new task set
- User can now submit answers and get scored

---

## 6. GET FILTERED CONTENT SETS

**Purpose:** Get content sets with advanced filtering across all themes

**URL:** `GET /v1/management/curated/filtered`

**Query Parameters:**
- `page` (int, default: 1) - Page number
- `limit` (int, default: 20, max: 100) - Items per page
- `theme_ids` (array) - Filter by specific theme IDs
- `difficulty_levels` (array) - Filter by difficulty levels [1,2,3]
- `status` (string) - Filter by status
- `gentype` (string) - Filter by generation type
- `start_date` (string) - Start date filter (YYYY-MM-DD)
- `end_date` (string) - End date filter (YYYY-MM-DD)

**cURL Example:**
```bash
curl -X 'GET' \
  'http://localhost:8204/v1/management/curated/filtered?page=1&limit=10&theme_ids=685e2955f928ae494af5e969&difficulty_levels=1,2' \
  -H 'accept: application/json' \
  -H 'Authorization: Bearer <your_jwt_token>'
```

**Response (200 OK):**
```json
{
  "data": [
    {
      "_id": "685e6d3b15fd9122a46a32b3",
      "title": "नेपाली संस्कृति र परम्परा - सेट 1",
      "theme_name": "नेपाली संस्कृति र परम्परा",
      "difficulty_level": 1,
      "total_tasks": 6,
      "total_score": 60,
      "status": "active"
    }
  ],
  "meta": {
    "page": 1,
    "limit": 10,
    "total": 150,
    "total_pages": 15
  }
}
```

---

## 7. SUBMIT TASK ITEM ANSWER

**Purpose:** Submit an answer for a single task item and get immediate scoring

**URL:** `POST /v1/management/tasks/submit/task-item`

**Request Body:**
```json
{
  "task_id": "675a1b2c3d4e5f6789abcdef",
  "answer": "b",
  "task_type": "single_choice",
  "folder": null
}
```

**cURL Example:**
```bash
curl -X 'POST' \
  'http://localhost:8204/v1/management/tasks/submit/task-item' \
  -H 'accept: application/json' \
  -H 'Authorization: Bearer <your_jwt_token>' \
  -H 'Content-Type: application/json' \
  -d '{
    "task_id": "675a1b2c3d4e5f6789abcdef",
    "answer": "b",
    "task_type": "single_choice",
    "folder": null
  }'
```

**Response (200 OK):**
```json
{
  "success": true,
  "data": {
    "task_id": "675a1b2c3d4e5f6789abcdef",
    "is_correct": true,
    "correct_answer": "b",
    "scored": 10,
    "total_score": 10,
    "feedback": "सही जवाफ! तपाईंले १० अंक पाउनुभयो।",
    "is_already_completed": false
  },
  "meta": {
    "timestamp": "2025-06-27T12:30:00Z"
  }
}
```

**What this does:**
- Updates the task item with user's answer and score
- Updates the parent task set's total scored points
- Increments attempted_tasks count on first submission
- Returns immediate feedback and scoring information

---

## 8. SUBMIT COMPLETE TASK SET

**Purpose:** Submit answers for all tasks in a task set at once

**URL:** `POST /v1/management/tasks/submit/task-set/{task_set_id}`

**Path Parameters:**
- `task_set_id` (string) - Task set ObjectId

**Request Body:**
```json
{
  "answers": [
    {
      "task_id": "675a1b2c3d4e5f6789abcdef",
      "answer": "b"
    },
    {
      "task_id": "675a1b2c3d4e5f6789abcdeg",
      "answer": ["a", "c"]
    }
  ]
}
```

**cURL Example:**
```bash
curl -X 'POST' \
  'http://localhost:8204/v1/management/tasks/submit/task-set/675a1b2c3d4e5f6789abcdef' \
  -H 'accept: application/json' \
  -H 'Authorization: Bearer <your_jwt_token>' \
  -H 'Content-Type: application/json' \
  -d '{
    "answers": [
      {
        "task_id": "675a1b2c3d4e5f6789abcdef",
        "answer": "b"
      },
      {
        "task_id": "675a1b2c3d4e5f6789abcdeg",
        "answer": ["a", "c"]
      }
    ]
  }'
```

**Response (200 OK):**
```json
{
  "success": true,
  "data": {
    "submission_id": "675a1b2c3d4e5f6789abcdeh",
    "task_set_id": "675a1b2c3d4e5f6789abcdef",
    "total_score": 60,
    "scored": 45,
    "percentage": 75,
    "correct_answers": 4,
    "total_questions": 6,
    "status": "completed"
  },
  "meta": {
    "timestamp": "2025-06-27T12:35:00Z"
  }
}
```

---

## 9. GET FILTER OPTIONS

**Purpose:** Get available filter values for dynamic filtering UI

**URL:** `GET /v1/management/curated/filter/curated`

**cURL Example:**
```bash
curl -X 'GET' \
  'http://localhost:8204/v1/management/curated/filter/curated' \
  -H 'accept: application/json' \
  -H 'Authorization: Bearer <your_jwt_token>'
```

**Response (200 OK):**
```json
{
  "success": true,
  "data": {
    "themes": [
      {
        "id": "685e2955f928ae494af5e969",
        "name": "नेपाली संस्कृति र परम्परा",
        "name_en": "Nepali Culture and Traditions"
      }
    ],
    "status_options": ["pending", "completed", "active"],
    "gentype_options": ["primary", "follow_up", "curated"],
    "difficulty_levels": [1, 2, 3],
    "date_range": {
      "earliest_date": "2025-01-01",
      "latest_date": "2025-06-27",
      "format": "YYYY-MM-DD"
    }
  }
}
```

---

## 10. EDITOR - GENERATE CURATED CONTENT

**Purpose:** Generate new curated content using AI (Editor Panel)

**URL:** `POST /v1/management/curated/generate`

**Request Body:**
```json
{
  "content": "नेपालको राष्ट्रिय चराको बारेमा प्रश्नहरू बनाउनुहोस्"
}
```

**cURL Example:**
```bash
curl -X 'POST' \
  'http://localhost:8204/v1/management/curated/generate' \
  -H 'accept: application/json' \
  -H 'Authorization: Bearer <your_jwt_token>' \
  -H 'Content-Type: application/json' \
  -d '{
    "content": "नेपालको राष्ट्रिय चराको बारेमा प्रश्नहरू बनाउनुहोस्"
  }'
```

**Response (200 OK):**
```json
{
  "content": "नेपालको राष्ट्रिय चराको बारेमा प्रश्नहरू बनाउनुहोस्",
  "generated_by": "68391d86b8b0e7ec9ababfbb",
  "status": "success",
  "message": "Content generated successfully"
}
```

**What this does:**
- Saves the prompt to editor_prompts collection
- Triggers background content generation process
- Returns immediate confirmation

---

## 11. EDITOR - GET PROMPTS

**Purpose:** Get all prompts created by the current editor user

**URL:** `POST /v1/management/curated/get_prompts`

**cURL Example:**
```bash
curl -X 'POST' \
  'http://localhost:8204/v1/management/curated/get_prompts' \
  -H 'accept: application/json' \
  -H 'Authorization: Bearer <your_jwt_token>' \
  -H 'Content-Type: application/json'
```

**Response (200 OK):**
```json
{
  "data": [
    {
      "_id": "675a1b2c3d4e5f6789abcdef",
      "content": "नेपालको राष्ट्रिय चराको बारेमा प्रश्नहरू बनाउनुहोस्",
      "user_id": "68391d86b8b0e7ec9ababfbb",
      "task_set_id": "675a1b2c3d4e5f6789abcdeg",
      "created_at": "2025-06-27T10:30:00Z",
      "status": "pending"
    }
  ]
}
```

---

## 12. GET THEME FILTER OPTIONS

**Purpose:** Get filter options specifically for themes

**URL:** `GET /v1/management/curated/filter/themes`

**cURL Example:**
```bash
curl -X 'GET' \
  'http://localhost:8204/v1/management/curated/filter/themes' \
  -H 'accept: application/json' \
  -H 'Authorization: Bearer <your_jwt_token>'
```

**Response (200 OK):**
```json
{
  "success": true,
  "data": {
    "categories": [
      "संस्कृति",
      "भूगोल",
      "इतिहास",
      "चाडपर्व",
      "खानपान"
    ],
    "is_active_options": [true, false],
    "sort_options": ["asc", "desc"],
    "date_filter_info": {
      "earliest_date": "2025-01-01",
      "latest_date": "2025-06-27",
      "format": "YYYY-MM-DD"
    }
  }
}

**Purpose:** Get filter options specifically for themes

**URL:** `GET /v1/management/curated/filter/themes`

**cURL Example:**
```bash
curl -X 'GET' \
  'http://localhost:8204/v1/management/curated/filter/themes' \
  -H 'accept: application/json' \
  -H 'Authorization: Bearer <your_jwt_token>'
```

**Response (200 OK):**
```json
{
  "success": true,
  "data": {
    "categories": [
      "संस्कृति",
      "भूगोल",
      "इतिहास",
      "चाडपर्व",
      "खानपान"
    ],
    "is_active_options": [true, false],
    "sort_options": ["asc", "desc"],
    "date_filter_info": {
      "earliest_date": "2025-01-01",
      "latest_date": "2025-06-27",
      "format": "YYYY-MM-DD"
    }
  }
}
```


13 Get TODAYS THEME

**Purpose:** Get random theme and content set for daily dashboard

**URL:** `GET /v1/management/curated/get_todays_theme`

**cURL Example:**
```bash
curl -X 'GET' \
  'http://localhost:8204/v1/management/curated/get_todays_theme' \
  -H 'accept: application/json' \
  -H 'Authorization: Bearer <your_jwt_token>'
```

**Response (200 OK):**
```json
{
  "success": true,
  "data": {
    "curated_set_id": "685e6d3b15fd9122a46a32b3",
    "theme_color": "#FF6B6B",
    "title": "नेपाली संस्कृति र परम्परा - सेट 1"
  },
  "message": "Today's theme selected",
  "metadata": {
    "timestamp": null,
    "request_id": null
  }
}
```