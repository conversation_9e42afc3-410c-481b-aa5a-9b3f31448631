import asyncio
from datetime import datetime, timezone
from pymongo import AsyncMongoClient
from bson import ObjectId

# Database configuration
MONGO_URI = "mongodb+srv://diwas:<EMAIL>/"
DB_NAME = "test_nepali_app"
USER_ID = ObjectId('68391d86b8b0e7ec9ababfbb')

async def fix_theme1_complete_all_questions():
    """Fix Theme 1 by adding all questions to all 5 sets"""
    
    client = AsyncMongoClient(MONGO_URI)
    
    try:
        db = client[DB_NAME]
        
        # Clear and recreate everything for Theme 1
        await db.themes.delete_many({"name": "शैक्षिक भ्रमण"})
        await db.curated_content_set.delete_many({})
        await db.curated_content_items.delete_many({})
        print("Cleared existing Theme 1 data")
        
        # Insert Theme 1: Educational Tours
        theme = {
            "name": "शैक्षिक भ्रमण",
            "name_en": "Educational Tours", 
            "description": "विभिन्न शैक्षिक स्थानहरूको भ्रमण र सिकाइ",
            "description_en": "Educational visits and learning at various academic sites",
            "order": 1,
            "is_active": True,
            "created_at": datetime.now(timezone.utc),
            "updated_at": datetime.now(timezone.utc),
            "category": "इतिहास",
            "category_en": "History",
            "color": "#8B4513",
            "icon": "🎓"
        }
        
        theme_result = await db.themes.insert_one(theme)
        theme_id = theme_result.inserted_id
        print(f"Theme inserted with ID: {theme_id}")
        
        # Create all 5 sets with complete questions
        all_sets_data = []
        
        # SET 1: Darjeeling School Tour
        set1_data = {
            "set_info": {
                "user_id": str(USER_ID),
                "input_type": "text",
                "input_content": {
                    "script": "म दिवास हुँ, रौतहट जिल्लाको गरुडा माध्यमिक विद्यालयमा कक्षा ९ मा पढ्छु। हाम्रो स्कूलले पहिलो पटक दार्जिलिङको शैक्षिक भ्रमणको आयोजना गर्यो। बिहान ५ बजे स्कूलबाट बस छुट्यो। हामी ३५ जना विद्यार्थी र ३ जना शिक्षक थियौं। पहिले हामी काकडभिट्टा पुग्यौं, त्यहाँ भारतीय सीमा पार गर्यौं। सिलिगुडी हुँदै दार्जिलिङ पुग्न ६ घण्टा लाग्यो। पहाडी बाटोमा घुम्ती सडकहरू देखेर धेरै रोमाञ्चक लाग्यो। दार्जिलिङ पुगेपछि हामीले हिमालयन माउन्टेनियरिङ इन्स्टिच्युट भ्रमण गर्यौं। त्यहाँ तेन्जिङ नोर्गे र एडमन्ड हिलारीको बारेमा जान्यौं। भोलिपल्ट बिहान टाइगर हिलबाट कञ्चनजङ्घाको सूर्योदय हेर्यौं। त्यो दृश्य अविस्मरणीय थियो।",
                    "title": "दार्जिलिङ स्कूल भ्रमण",
                    "title_en": "Darjeeling School Tour",
                    "location": "दार्जिलिङ",
                    "character": "दिवास",
                    "school": "गरुडा माध्यमिक विद्यालय",
                    "district": "रौतहट"
                },
                "metadata": {
                    "theme_id": str(theme_id),
                    "script_type": "educational_tour",
                    "generated_by": "curated_content"
                }
            },
            "questions": [
                {
                    "type": "single_choice",
                    "title": "दिवास कुन जिल्लाको विद्यार्थी हो?",
                    "question": {
                        "text": "दिवास कुन जिल्लाको विद्यार्थी हो?",
                        "translated_text": "Which district is Diwas a student from?",
                        "options": ["रौतहट", "सिरहा", "महोत्तरी", "सप्तरी"],
                        "answer_hint": "स्क्रिप्टमा उल्लेख गरिएको जिल्लाको नाम हेर्नुहोस्",
                        "metadata": {}
                    },
                    "correct_answer": {"value": "रौतहट", "type": "single_choice"}
                },
                {
                    "type": "single_choice",
                    "title": "भ्रमणमा कति जना विद्यार्थी सहभागी थिए?",
                    "question": {
                        "text": "भ्रमणमा कति जना विद्यार्थी सहभागी थिए?",
                        "translated_text": "How many students participated in the tour?",
                        "options": ["३०", "३५", "४०", "२५"],
                        "answer_hint": "स्क्रिप्टमा उल्लेखित विद्यार्थी संख्या हेर्नुहोस्",
                        "metadata": {}
                    },
                    "correct_answer": {"value": "३५", "type": "single_choice"}
                },
                {
                    "type": "single_choice",
                    "title": "दार्जिलिङ पुग्न कति घण्टा लाग्यो?",
                    "question": {
                        "text": "दार्जिलिङ पुग्न कति घण्टा लाग्यो?",
                        "translated_text": "How many hours did it take to reach Darjeeling?",
                        "options": ["५ घण्टा", "६ घण्टा", "७ घण्टा", "८ घण्टा"],
                        "answer_hint": "यात्राको समयावधि स्क्रिप्टमा उल्लेख छ",
                        "metadata": {}
                    },
                    "correct_answer": {"value": "६ घण्टा", "type": "single_choice"}
                },
                {
                    "type": "multiple_choice",
                    "title": "हिमालयन माउन्टेनियरिङ इन्स्टिच्युटमा कसका बारेमा जानकारी पाए?",
                    "question": {
                        "text": "हिमालयन माउन्टेनियरिङ इन्स्टिच्युटमा कसका बारेमा जानकारी पाए? (दुई उत्तर छान्नुहोस्)",
                        "translated_text": "Who did they learn about at the Himalayan Mountaineering Institute? (Choose two answers)",
                        "options": ["तेन्जिङ नोर्गे", "एडमन्ड हिलारी", "जुङ्गो ताबेई", "रेनहोल्ड मेसनर"],
                        "answer_hint": "स्क्रिप्टमा उल्लेखित दुई व्यक्तित्व छान्नुहोस्",
                        "metadata": {}
                    },
                    "correct_answer": {"value": ["तेन्जिङ नोर्गे", "एडमन्ड हिलारी"], "type": "multiple_choice"}
                },
                {
                    "type": "single_choice",
                    "title": "कुन ठाउँबाट कञ्चनजङ्घाको सूर्योदय हेरे?",
                    "question": {
                        "text": "कुन ठाउँबाट कञ्चनजङ्घाको सूर्योदय हेरे?",
                        "translated_text": "From which place did they watch the sunrise over Kanchenjunga?",
                        "options": ["टाइगर हिल", "डार्जिलिङ मल", "हिमालयन इन्स्टिच्युट", "बाटासिया लुप"],
                        "answer_hint": "सूर्योदय हेर्ने प्रसिद्ध स्थान",
                        "metadata": {}
                    },
                    "correct_answer": {"value": "टाइगर हिल", "type": "single_choice"}
                },
                {
                    "type": "single_choice",
                    "title": "भारतीय सीमा कुन ठाउँमा पार गरे?",
                    "question": {
                        "text": "भारतीय सीमा कुन ठाउँमा पार गरे?",
                        "translated_text": "Where did they cross the Indian border?",
                        "options": ["काकडभिट्टा", "बिर्गञ्ज", "भैरहवा", "महेन्द्रनगर"],
                        "answer_hint": "पूर्वी सीमानाका प्रमुख नाका",
                        "metadata": {}
                    },
                    "correct_answer": {"value": "काकडभिट्टा", "type": "single_choice"}
                }
            ]
        }
        
        # SET 2: Kathmandu Science Museum
        set2_data = {
            "set_info": {
                "user_id": str(USER_ID),
                "input_type": "text",
                "input_content": {
                    "script": "म सरिता हुँ, धनकुटाको सरस्वती माध्यमिक विद्यालयकी कक्षा १० की विद्यार्थी। हाम्रो विज्ञान विषयको लागि काठमाडौंको राष्ट्रिय विज्ञान संग्रहालय भ्रमण गर्यौं। त्यहाँ पुगेपछि पहिले हामी भौतिक विज्ञानको खण्डमा गयौं। त्यहाँ बिजुलीको प्रयोग, चुम्बकत्व र प्रकाशका बारेमा प्रयोगहरू थिए। सबैभन्दा रोचक कुरा के थियो भने त्यहाँ भ्यान डे ग्राफ जेनेरेटर थियो जसले स्थिर बिजुली बनाउँछ। त्यसपछि हामी रसायन विज्ञानको खण्डमा गयौं। त्यहाँ आवर्त सारणी र विभिन्न तत्वहरूका नमूनाहरू थिए। जीवविज्ञानको खण्डमा मानव शरीरका अंगहरूको मोडेल र डायनासोरका हड्डीहरू थिए। अन्तमा हामी तारामण्डलमा गयौं र ग्रह-नक्षत्रहरूको बारेमा जान्यौं।",
                    "title": "काठमाडौं विज्ञान संग्रहालय भ्रमण",
                    "title_en": "Kathmandu Science Museum Visit",
                    "location": "काठमाडौं",
                    "character": "सरिता",
                    "school": "सरस्वती माध्यमिक विद्यालय",
                    "district": "धनकुटा"
                },
                "metadata": {
                    "theme_id": str(theme_id),
                    "script_type": "educational_tour",
                    "generated_by": "curated_content"
                }
            },
            "questions": [
                {
                    "type": "single_choice",
                    "title": "सरिता कुन जिल्लाकी विद्यार्थी हिन्?",
                    "question": {
                        "text": "सरिता कुन जिल्लाकी विद्यार्थी हिन्?",
                        "translated_text": "Which district is Sarita a student from?",
                        "options": ["धनकुटा", "इलाम", "तेह्रथुम", "संखुवासभा"],
                        "answer_hint": "स्क्रिप्टको सुरुमा उल्लेख छ",
                        "metadata": {}
                    },
                    "correct_answer": {"value": "धनकुटा", "type": "single_choice"}
                },
                {
                    "type": "multiple_choice",
                    "title": "विज्ञान संग्रहालयमा कुन कुन खण्डहरू थिए?",
                    "question": {
                        "text": "विज्ञान संग्रहालयमा कुन कुन खण्डहरू थिए? (तीनवटा छान्नुहोस्)",
                        "translated_text": "Which sections were there in the science museum? (Choose three)",
                        "options": ["भौतिक विज्ञान", "रसायन विज्ञान", "जीवविज्ञान", "गणित"],
                        "answer_hint": "स्क्रिप्टमा उल्लेखित तीनवटा खण्डहरू",
                        "metadata": {}
                    },
                    "correct_answer": {"value": ["भौतिक विज्ञान", "रसायन विज्ञान", "जीवविज्ञान"], "type": "multiple_choice"}
                },
                {
                    "type": "single_choice",
                    "title": "भ्यान डे ग्राफ जेनेरेटरले के बनाउँछ?",
                    "question": {
                        "text": "भ्यान डे ग्राफ जेनेरेटरले के बनाउँछ?",
                        "translated_text": "What does the Van de Graaff generator produce?",
                        "options": ["स्थिर बिजुली", "चुम्बकीय क्षेत्र", "प्रकाश", "आवाज"],
                        "answer_hint": "स्क्रिप्टमा उल्लेखित रोचक कुरा",
                        "metadata": {}
                    },
                    "correct_answer": {"value": "स्थिर बिजुली", "type": "single_choice"}
                },
                {
                    "type": "single_choice",
                    "title": "जीवविज्ञानको खण्डमा कुन जनावरका हड्डीहरू थिए?",
                    "question": {
                        "text": "जीवविज्ञानको खण्डमा कुन जनावरका हड्डीहरू थिए?",
                        "translated_text": "Which animal's bones were in the biology section?",
                        "options": ["डायनासोर", "हात्ती", "बाघ", "गैंडा"],
                        "answer_hint": "प्राचीन जनावरका हड्डीहरू",
                        "metadata": {}
                    },
                    "correct_answer": {"value": "डायनासोर", "type": "single_choice"}
                },
                {
                    "type": "single_choice",
                    "title": "अन्तमा कुन ठाउँमा गए?",
                    "question": {
                        "text": "अन्तमा कुन ठाउँमा गए?",
                        "translated_text": "Where did they go at the end?",
                        "options": ["तारामण्डल", "पुस्तकालय", "प्रयोगशाला", "कार्यालय"],
                        "answer_hint": "ग्रह-नक्षत्र हेर्ने ठाउँ",
                        "metadata": {}
                    },
                    "correct_answer": {"value": "तारामण्डल", "type": "single_choice"}
                },
                {
                    "type": "single_choice",
                    "title": "तारामण्डलमा के बारेमा जाने?",
                    "question": {
                        "text": "तारामण्डलमा के बारेमा जाने?",
                        "translated_text": "What did they learn about in the planetarium?",
                        "options": ["ग्रह-नक्षत्र", "मौसम", "समुद्र", "जंगल"],
                        "answer_hint": "आकाशमा देखिने वस्तुहरू",
                        "metadata": {}
                    },
                    "correct_answer": {"value": "ग्रह-नक्षत्र", "type": "single_choice"}
                }
            ]
        }
        
        # SET 3: Pokhara Mountain Museum
        set3_data = {
            "set_info": {
                "user_id": str(USER_ID),
                "input_type": "text",
                "input_content": {
                    "script": "म राम हुँ, कास्कीको आदर्श माध्यमिक विद्यालयको विद्यार्थी। हाम्रो भूगोल कक्षाको लागि पोखराको अन्तर्राष्ट्रिय पर्वतारोहण संग्रहालय भ्रमण गर्यौं। यो संग्रहालय २००४ मा स्थापना भएको थियो। त्यहाँ पुगेपछि गाइडले नेपालका अष्टसहस्री पर्वतहरूको बारेमा बताए। एभरेस्ट, कञ्चनजङ्घा, ल्होत्से, मकालु, चो ओयु, धौलागिरी, मनास्लु र अन्नपूर्णा - यी सबै नेपालमा छन्। त्यहाँ पर्वतारोहीहरूले प्रयोग गर्ने उपकरणहरू थिए - रस्सी, बुट, अक्सिजन मास्क, टेन्ट। सबैभन्दा रोचक कुरा के थियो भने त्यहाँ शेर्पाहरूको संस्कृति र परम्पराको बारेमा जानकारी थियो।",
                    "title": "पोखरा अन्तर्राष्ट्रिय पर्वतारोहण संग्रहालय भ्रमण",
                    "title_en": "Pokhara International Mountain Museum Visit",
                    "location": "पोखरा",
                    "character": "राम",
                    "school": "आदर्श माध्यमिक विद्यालय",
                    "district": "कास्की"
                },
                "metadata": {
                    "theme_id": str(theme_id),
                    "script_type": "educational_tour",
                    "generated_by": "curated_content"
                }
            },
            "questions": [
                {
                    "type": "single_choice",
                    "title": "राम कुन जिल्लाको विद्यार्थी हो?",
                    "question": {
                        "text": "राम कुन जिल्लाको विद्यार्थी हो?",
                        "translated_text": "Which district is Ram a student from?",
                        "options": ["कास्की", "स्याङ्जा", "तनहुँ", "लमजुङ"],
                        "answer_hint": "स्क्रिप्टको सुरुमा उल्लेख छ",
                        "metadata": {}
                    },
                    "correct_answer": {"value": "कास्की", "type": "single_choice"}
                },
                {
                    "type": "single_choice",
                    "title": "पर्वतारोहण संग्रहालय कहिले स्थापना भएको थियो?",
                    "question": {
                        "text": "पर्वतारोहण संग्रहालय कहिले स्थापना भएको थियो?",
                        "translated_text": "When was the mountaineering museum established?",
                        "options": ["२००२", "२००४", "२००६", "२००८"],
                        "answer_hint": "स्क्रिप्टमा उल्लेखित वर्ष",
                        "metadata": {}
                    },
                    "correct_answer": {"value": "२००४", "type": "single_choice"}
                },
                {
                    "type": "multiple_choice",
                    "title": "नेपालका अष्टसहस्री पर्वतहरू कुन कुन हुन्?",
                    "question": {
                        "text": "नेपालका अष्टसहस्री पर्वतहरू कुन कुन हुन्? (चारवटा छान्नुहोस्)",
                        "translated_text": "Which are the eight-thousander mountains of Nepal? (Choose four)",
                        "options": ["एभरेस्ट", "कञ्चनजङ्घा", "धौलागिरी", "अन्नपूर्णा", "के२", "नाङ्गा पर्वत"],
                        "answer_hint": "स्क्रिप्टमा उल्लेखित नेपालका पर्वतहरू",
                        "metadata": {}
                    },
                    "correct_answer": {"value": ["एभरेस्ट", "कञ्चनजङ्घा", "धौलागिरी", "अन्नपूर्णा"], "type": "multiple_choice"}
                },
                {
                    "type": "multiple_choice",
                    "title": "पर्वतारोहीहरूले प्रयोग गर्ने उपकरणहरू कुन कुन थिए?",
                    "question": {
                        "text": "पर्वतारोहीहरूले प्रयोग गर्ने उपकरणहरू कुन कुन थिए? (चारवटा छान्नुहोस्)",
                        "translated_text": "What equipment do mountaineers use? (Choose four)",
                        "options": ["रस्सी", "बुट", "अक्सिजन मास्क", "टेन्ट", "साइकल", "कार"],
                        "answer_hint": "स्क्रिप्टमा उल्लेखित उपकरणहरू",
                        "metadata": {}
                    },
                    "correct_answer": {"value": ["रस्सी", "बुट", "अक्सिजन मास्क", "टेन्ट"], "type": "multiple_choice"}
                },
                {
                    "type": "single_choice",
                    "title": "संग्रहालयमा कुन समुदायको संस्कृतिको बारेमा जानकारी थियो?",
                    "question": {
                        "text": "संग्रहालयमा कुन समुदायको संस्कृतिको बारेमा जानकारी थियो?",
                        "translated_text": "Which community's culture was featured in the museum?",
                        "options": ["शेर्पा", "गुरुङ", "मगर", "तामाङ"],
                        "answer_hint": "पर्वतारोहणसँग जोडिएको समुदाय",
                        "metadata": {}
                    },
                    "correct_answer": {"value": "शेर्पा", "type": "single_choice"}
                },
                {
                    "type": "single_choice",
                    "title": "राम कुन विषयको कक्षाको लागि भ्रमण गरे?",
                    "question": {
                        "text": "राम कुन विषयको कक्षाको लागि भ्रमण गरे?",
                        "translated_text": "For which subject's class did Ram visit?",
                        "options": ["भूगोल", "इतिहास", "विज्ञान", "नेपाली"],
                        "answer_hint": "स्क्रिप्टमा उल्लेखित विषय",
                        "metadata": {}
                    },
                    "correct_answer": {"value": "भूगोल", "type": "single_choice"}
                }
            ]
        }

        # SET 4: Janakpur Cultural Study
        set4_data = {
            "set_info": {
                "user_id": str(USER_ID),
                "input_type": "text",
                "input_content": {
                    "script": "म गीता हुँ, धनुषाको राम जानकी माध्यमिक विद्यालयकी विद्यार्थी। हाम्रो नेपाली साहित्य र संस्कृतिको कक्षाको लागि जनकपुरको शैक्षिक भ्रमण गर्यौं। जनकपुर सीताको जन्मस्थान हो र यहाँ रामायणको कथा जोडिएको छ। पहिले हामी जानकी मन्दिर गयौं। यो मन्दिर १९११ मा निर्माण भएको थियो र यो राजस्थानी शैलीमा बनेको छ। त्यहाँ सीता र रामको मूर्ति छ। गाइडले भन्यो कि यहाँ हरेक वर्ष विवाह पञ्चमीमा ठूलो मेला लाग्छ। त्यसपछि हामी रामसागर तालमा गयौं। यो ताल राजा जनकले बनाएका थिए। जनकपुरमा मिथिला कलाको परम्परा छ।",
                    "title": "जनकपुर जानकी मन्दिर र संस्कृति अध्ययन",
                    "title_en": "Janakpur Janaki Temple and Cultural Study",
                    "location": "जनकपुर",
                    "character": "गीता",
                    "school": "राम जानकी माध्यमिक विद्यालय",
                    "district": "धनुषा"
                },
                "metadata": {
                    "theme_id": str(theme_id),
                    "script_type": "educational_tour",
                    "generated_by": "curated_content"
                }
            },
            "questions": [
                {
                    "type": "single_choice",
                    "title": "गीता कुन जिल्लाकी विद्यार्थी हिन्?",
                    "question": {
                        "text": "गीता कुन जिल्लाकी विद्यार्थी हिन्?",
                        "translated_text": "Which district is Gita a student from?",
                        "options": ["धनुषा", "सिरहा", "महोत्तरी", "सप्तरी"],
                        "answer_hint": "स्क्रिप्टको सुरुमा उल्लेख छ",
                        "metadata": {}
                    },
                    "correct_answer": {"value": "धनुषा", "type": "single_choice"}
                },
                {
                    "type": "single_choice",
                    "title": "जनकपुर कसको जन्मस्थान हो?",
                    "question": {
                        "text": "जनकपुर कसको जन्मस्थान हो?",
                        "translated_text": "Whose birthplace is Janakpur?",
                        "options": ["सीता", "राम", "लक्ष्मण", "हनुमान"],
                        "answer_hint": "रामायणकी मुख्य पात्र",
                        "metadata": {}
                    },
                    "correct_answer": {"value": "सीता", "type": "single_choice"}
                },
                {
                    "type": "single_choice",
                    "title": "जानकी मन्दिर कहिले निर्माण भएको थियो?",
                    "question": {
                        "text": "जानकी मन्दिर कहिले निर्माण भएको थियो?",
                        "translated_text": "When was Janaki Temple built?",
                        "options": ["१९०९", "१९११", "१९१३", "१९१५"],
                        "answer_hint": "स्क्रिप्टमा उल्लेखित वर्ष",
                        "metadata": {}
                    },
                    "correct_answer": {"value": "१९११", "type": "single_choice"}
                },
                {
                    "type": "single_choice",
                    "title": "जानकी मन्दिर कुन शैलीमा बनेको छ?",
                    "question": {
                        "text": "जानकी मन्दिर कुन शैलीमा बनेको छ?",
                        "translated_text": "In which architectural style is Janaki Temple built?",
                        "options": ["राजस्थानी", "नेवारी", "मुगल", "तिब्बती"],
                        "answer_hint": "स्क्रिप्टमा उल्लेखित वास्तुकला शैली",
                        "metadata": {}
                    },
                    "correct_answer": {"value": "राजस्थानी", "type": "single_choice"}
                },
                {
                    "type": "single_choice",
                    "title": "कुन चाडमा जनकपुरमा ठूलो मेला लाग्छ?",
                    "question": {
                        "text": "कुन चाडमा जनकपुरमा ठूलो मेला लाग्छ?",
                        "translated_text": "During which festival is there a big fair in Janakpur?",
                        "options": ["विवाह पञ्चमी", "दशैं", "तिहार", "होली"],
                        "answer_hint": "राम र सीताको विवाहसँग सम्बन्धित चाड",
                        "metadata": {}
                    },
                    "correct_answer": {"value": "विवाह पञ्चमी", "type": "single_choice"}
                },
                {
                    "type": "single_choice",
                    "title": "रामसागर ताल कसले बनाएका थिए?",
                    "question": {
                        "text": "रामसागर ताल कसले बनाएका थिए?",
                        "translated_text": "Who built Ramsagar pond?",
                        "options": ["राजा जनक", "राजा दशरथ", "राम", "लक्ष्मण"],
                        "answer_hint": "सीताका बुबा",
                        "metadata": {}
                    },
                    "correct_answer": {"value": "राजा जनक", "type": "single_choice"}
                }
            ]
        }

        # SET 5: Chitwan National Park
        set5_data = {
            "set_info": {
                "user_id": str(USER_ID),
                "input_type": "text",
                "input_content": {
                    "script": "म अर्जुन हुँ, चितवनको शहीद स्मृति माध्यमिक विद्यालयको विद्यार्थी। हाम्रो वातावरण विज्ञानको कक्षाको लागि चितवन राष्ट्रिय निकुञ्जको भ्रमण गर्यौं। यो निकुञ्ज १९७३ मा स्थापना भएको थियो र नेपालको पहिलो राष्ट्रिय निकुञ्ज हो। त्यहाँ पुगेपछि पहिले हामी निकुञ्जको मुख्यालयमा गयौं र गाइडले एक सींगे गैंडाको बारेमा बताए। नेपालमा करिब ७०० वटा गैंडा छन् र धेरैजसो चितवनमा छन्। त्यसपछि हामी जीप सफारीमा गयौं। त्यहाँ हामीले गैंडा, हात्ती, चितुवा र धेरै चराहरू देख्यौं। राप्ती नदीमा घडियाल र मगरमच्छ देख्यौं। गाइडले भन्यो कि यो निकुञ्ज युनेस्कोको विश्व सम्पदा सूचीमा छ।",
                    "title": "चितवन राष्ट्रिय निकुञ्ज शैक्षिक भ्रमण",
                    "title_en": "Chitwan National Park Educational Visit",
                    "location": "चितवन",
                    "character": "अर्जुन",
                    "school": "शहीद स्मृति माध्यमिक विद्यालय",
                    "district": "चितवन"
                },
                "metadata": {
                    "theme_id": str(theme_id),
                    "script_type": "educational_tour",
                    "generated_by": "curated_content"
                }
            },
            "questions": [
                {
                    "type": "single_choice",
                    "title": "अर्जुन कुन जिल्लाको विद्यार्थी हो?",
                    "question": {
                        "text": "अर्जुन कुन जिल्लाको विद्यार्थी हो?",
                        "translated_text": "Which district is Arjun a student from?",
                        "options": ["चितवन", "नवलपरासी", "पर्सा", "बारा"],
                        "answer_hint": "स्क्रिप्टको सुरुमा उल्लेख छ",
                        "metadata": {}
                    },
                    "correct_answer": {"value": "चितवन", "type": "single_choice"}
                },
                {
                    "type": "single_choice",
                    "title": "चितवन राष्ट्रिय निकुञ्ज कहिले स्थापना भएको थियो?",
                    "question": {
                        "text": "चितवन राष्ट्रिय निकुञ्ज कहिले स्थापना भएको थियो?",
                        "translated_text": "When was Chitwan National Park established?",
                        "options": ["१९७१", "१९७३", "१९७५", "१९७७"],
                        "answer_hint": "स्क्रिप्टमा उल्लेखित वर्ष",
                        "metadata": {}
                    },
                    "correct_answer": {"value": "१९७३", "type": "single_choice"}
                },
                {
                    "type": "single_choice",
                    "title": "यो नेपालको कौं संख्याको राष्ट्रिय निकुञ्ज हो?",
                    "question": {
                        "text": "यो नेपालको कौं संख्याको राष्ट्रिय निकुञ्ज हो?",
                        "translated_text": "What number national park is this in Nepal?",
                        "options": ["पहिलो", "दोस्रो", "तेस्रो", "चौथो"],
                        "answer_hint": "स्क्रिप्टमा उल्लेखित क्रम",
                        "metadata": {}
                    },
                    "correct_answer": {"value": "पहिलो", "type": "single_choice"}
                },
                {
                    "type": "single_choice",
                    "title": "नेपालमा करिब कति वटा गैंडा छन्?",
                    "question": {
                        "text": "नेपालमा करिब कति वटा गैंडा छन्?",
                        "translated_text": "Approximately how many rhinos are there in Nepal?",
                        "options": ["५००", "७००", "९००", "११००"],
                        "answer_hint": "स्क्रिप्टमा उल्लेखित संख्या",
                        "metadata": {}
                    },
                    "correct_answer": {"value": "७००", "type": "single_choice"}
                },
                {
                    "type": "multiple_choice",
                    "title": "जीप सफारीमा कुन कुन जनावरहरू देखे?",
                    "question": {
                        "text": "जीप सफारीमा कुन कुन जनावरहरू देखे? (तीनवटा छान्नुहोस्)",
                        "translated_text": "Which animals did they see in the jeep safari? (Choose three)",
                        "options": ["गैंडा", "हात्ती", "चितुवा", "बाघ", "भालु", "हिरण"],
                        "answer_hint": "स्क्रिप्टमा उल्लेखित जनावरहरू",
                        "metadata": {}
                    },
                    "correct_answer": {"value": ["गैंडा", "हात्ती", "चितुवा"], "type": "multiple_choice"}
                },
                {
                    "type": "multiple_choice",
                    "title": "राप्ती नदीमा कुन कुन जनावरहरू देखे?",
                    "question": {
                        "text": "राप्ती नदीमा कुन कुन जनावरहरू देखे? (दुईवटा छान्नुहोस्)",
                        "translated_text": "Which animals did they see in Rapti river? (Choose two)",
                        "options": ["घडियाल", "मगरमच्छ", "माछा", "कछुवा"],
                        "answer_hint": "स्क्रिप्टमा उल्लेखित पानीका जनावरहरू",
                        "metadata": {}
                    },
                    "correct_answer": {"value": ["घडियाल", "मगरमच्छ"], "type": "multiple_choice"}
                }
            ]
        }

        all_sets_data = [set1_data, set2_data, set3_data, set4_data, set5_data]
        
        # Process each set
        for set_data in all_sets_data:
            # Insert the curated content set
            set_info = set_data["set_info"]
            set_info.update({
                "input_data": None,
                "tasks": [],
                "created_at": datetime.now(timezone.utc),
                "submitted_at": None,
                "verified_at": None,
                "completed_at": None,
                "status": "pending",
                "difficulty": "medium",
                "total_tasks": len(set_data["questions"]),
                "attempted_tasks": 0,
                "total_score": len(set_data["questions"]) * 10,
                "scored": 0,
                "notes": None,
                "remark": None
            })
            
            set_result = await db.curated_content_set.insert_one(set_info)
            set_id = set_result.inserted_id
            
            # Insert questions for this set
            questions = []
            for q_data in set_data["questions"]:
                question = {
                    "task_set_id": set_id,
                    "user_id": USER_ID,
                    "type": q_data["type"],
                    "title": q_data["title"],
                    "question": q_data["question"],
                    "correct_answer": q_data["correct_answer"],
                    "user_answer": None,
                    "status": "pending",
                    "result": None,
                    "submitted": False,
                    "is_attempted": False,
                    "attempts_count": 0,
                    "total_score": 10,
                    "scored": 0,
                    "created_at": datetime.now(timezone.utc),
                    "updated_at": datetime.now(timezone.utc),
                    "metadata": {"difficulty_level": 1, "topic": "comprehension"}
                }
                questions.append(question)
            
            # Insert all questions
            question_results = await db.curated_content_items.insert_many(questions)
            question_ids = list(question_results.inserted_ids)
            
            # Update set with question IDs
            await db.curated_content_set.update_one(
                {"_id": set_id},
                {"$set": {"tasks": [str(qid) for qid in question_ids]}}
            )
            
            print(f"Set '{set_info['input_content']['title']}' created with {len(question_ids)} questions")
        
        print(f"\nTheme 1 completed successfully!")
        print(f"Theme ID: {theme_id}")
        print(f"Total sets: {len(all_sets_data)}")
        
    except Exception as error:
        print(f"Error: {error}")
    finally:
        await client.close()

if __name__ == "__main__":
    asyncio.run(fix_theme1_complete_all_questions())
