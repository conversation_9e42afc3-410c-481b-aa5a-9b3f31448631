import asyncio
from datetime import datetime, timezone
from pymongo import AsyncMongoClient
from bson import ObjectId

# Database configuration
MONGO_URI = "mongodb+srv://diwas:<EMAIL>/"
DB_NAME = "test_nepali_app"
USER_ID = ObjectId('68391d86b8b0e7ec9ababfbb')

async def insert_theme1_correct_structure():
    """Insert Theme 1: Educational Tours with correct structure matching task_sets and task_items"""
    
    client = AsyncMongoClient(MONGO_URI)
    
    try:
        db = client[DB_NAME]
        
        # Clear existing data first
        await db.themes.delete_many({})
        await db.curated_content_set.delete_many({})
        await db.curated_content_items.delete_many({})
        print("Cleared existing data")
        
        # Insert Theme 1: Educational Tours
        theme = {
            "name": "शैक्षिक भ्रमण",
            "name_en": "Educational Tours", 
            "description": "विभिन्न शैक्षिक स्थानहरूको भ्रमण र सिकाइ",
            "description_en": "Educational visits and learning at various academic sites",
            "order": 1,
            "is_active": True,
            "created_at": datetime.now(timezone.utc),
            "updated_at": datetime.now(timezone.utc),
            "category": "इतिहास",
            "category_en": "History",
            "color": "#8B4513",
            "icon": "🎓"
        }
        
        theme_result = await db.themes.insert_one(theme)
        theme_id = theme_result.inserted_id
        print(f"Theme inserted with ID: {theme_id}")
        
        # Set 1: Darjeeling School Tour (following task_sets structure exactly)
        set1 = {
            "user_id": str(USER_ID),  # String format like existing
            "input_type": "text",
            "input_content": {  # DICT format with script field
                "script": "म दिवास हुँ, रौतहट जिल्लाको गरुडा माध्यमिक विद्यालयमा कक्षा ९ मा पढ्छु। हाम्रो स्कूलले पहिलो पटक दार्जिलिङको शैक्षिक भ्रमणको आयोजना गर्यो। बिहान ५ बजे स्कूलबाट बस छुट्यो। हामी ३५ जना विद्यार्थी र ३ जना शिक्षक थियौं। पहिले हामी काकडभिट्टा पुग्यौं, त्यहाँ भारतीय सीमा पार गर्यौं। सिलिगुडी हुँदै दार्जिलिङ पुग्न ६ घण्टा लाग्यो। पहाडी बाटोमा घुम्ती सडकहरू देखेर धेरै रोमाञ्चक लाग्यो। दार्जिलिङ पुगेपछि हामीले हिमालयन माउन्टेनियरिङ इन्स्टिच्युट भ्रमण गर्यौं। त्यहाँ तेन्जिङ नोर्गे र एडमन्ड हिलारीको बारेमा जान्यौं। भोलिपल्ट बिहान टाइगर हिलबाट कञ्चनजङ्घाको सूर्योदय हेर्यौं। त्यो दृश्य अविस्मरणीय थियो। दार्जिलिङको चिया बगानमा पनि गयौं र चिया बनाउने प्रक्रिया देख्यौं।",
                "title": "दार्जिलिङ स्कूल भ्रमण",
                "title_en": "Darjeeling School Tour",
                "location": "दार्जिलिङ",
                "character": "दिवास",
                "school": "गरुडा माध्यमिक विद्यालय",
                "district": "रौतहट"
            },
            "input_data": None,
            "tasks": [],  # Will be populated with task item IDs
            "created_at": datetime.now(timezone.utc),
            "submitted_at": None,
            "verified_at": None,
            "completed_at": None,
            "status": "pending",
            "difficulty": "medium",
            "total_tasks": 6,
            "attempted_tasks": 0,
            "total_score": 60,  # 6 questions × 10 points each
            "scored": 0,
            "notes": None,
            "remark": None,
            "metadata": {
                "theme_id": str(theme_id),
                "script_type": "educational_tour",
                "generated_by": "curated_content",
                "content_category": "history_education",
                "target_audience": "grade_9_students",
                "learning_objectives": ["geography_knowledge", "cultural_awareness", "travel_experience"]
            }
        }
        
        # Insert the curated content set
        set_result = await db.curated_content_set.insert_one(set1)
        set1_id = set_result.inserted_id
        print(f"Curated content set inserted with ID: {set1_id}")
        
        # Create questions for Set 1 (following task_items structure exactly)
        questions_set1 = [
            {
                "task_set_id": set1_id,  # ObjectId reference
                "user_id": USER_ID,     # ObjectId format like existing
                "type": "single_choice",
                "title": "दिवास कुन जिल्लाको विद्यार्थी हो?",
                "question": {
                    "text": "दिवास कुन जिल्लाको विद्यार्थी हो?",
                    "translated_text": "Which district is Diwas a student from?",
                    "options": ["रौतहट", "सिरहा", "महोत्तरी", "सप्तरी"],  # Array format
                    "answer_hint": "स्क्रिप्टमा उल्लेख गरिएको जिल्लाको नाम हेर्नुहोस्",
                    "metadata": {}
                },
                "correct_answer": {
                    "value": "रौतहट",  # Single value for single_choice
                    "type": "single_choice"
                },
                "user_answer": None,
                "status": "pending",
                "result": None,
                "submitted": False,
                "is_attempted": False,
                "attempts_count": 0,
                "total_score": 10,
                "scored": 0,
                "created_at": datetime.now(timezone.utc),
                "updated_at": datetime.now(timezone.utc),
                "metadata": {
                    "difficulty_level": 1,
                    "topic": "geography",
                    "learning_objective": "location_identification"
                }
            },
            {
                "task_set_id": set1_id,
                "user_id": USER_ID,
                "type": "single_choice",
                "title": "भ्रमणमा कति जना विद्यार्थी सहभागी थिए?",
                "question": {
                    "text": "भ्रमणमा कति जना विद्यार्थी सहभागी थिए?",
                    "translated_text": "How many students participated in the tour?",
                    "options": ["३०", "३५", "४०", "२५"],
                    "answer_hint": "स्क्रिप्टमा उल्लेखित विद्यार्थी संख्या हेर्नुहोस्",
                    "metadata": {}
                },
                "correct_answer": {
                    "value": "३५",
                    "type": "single_choice"
                },
                "user_answer": None,
                "status": "pending",
                "result": None,
                "submitted": False,
                "is_attempted": False,
                "attempts_count": 0,
                "total_score": 10,
                "scored": 0,
                "created_at": datetime.now(timezone.utc),
                "updated_at": datetime.now(timezone.utc),
                "metadata": {
                    "difficulty_level": 1,
                    "topic": "reading_comprehension",
                    "learning_objective": "detail_extraction"
                }
            },
            {
                "task_set_id": set1_id,
                "user_id": USER_ID,
                "type": "single_choice",
                "title": "दार्जिलिङ पुग्न कति घण्टा लाग्यो?",
                "question": {
                    "text": "दार्जिलिङ पुग्न कति घण्टा लाग्यो?",
                    "translated_text": "How many hours did it take to reach Darjeeling?",
                    "options": ["५ घण्टा", "६ घण्टा", "७ घण्टा", "८ घण्टा"],
                    "answer_hint": "यात्राको समयावधि स्क्रिप्टमा उल्लेख छ",
                    "metadata": {}
                },
                "correct_answer": {
                    "value": "६ घण्टा",
                    "type": "single_choice"
                },
                "user_answer": None,
                "status": "pending",
                "result": None,
                "submitted": False,
                "is_attempted": False,
                "attempts_count": 0,
                "total_score": 10,
                "scored": 0,
                "created_at": datetime.now(timezone.utc),
                "updated_at": datetime.now(timezone.utc),
                "metadata": {
                    "difficulty_level": 1,
                    "topic": "time_calculation",
                    "learning_objective": "numerical_comprehension"
                }
            },
            {
                "task_set_id": set1_id,
                "user_id": USER_ID,
                "type": "multiple_choice",  # Multiple choice example
                "title": "हिमालयन माउन्टेनियरिङ इन्स्टिच्युटमा कसका बारेमा जानकारी पाए?",
                "question": {
                    "text": "हिमालयन माउन्टेनियरिङ इन्स्टिच्युटमा कसका बारेमा जानकारी पाए? (दुई उत्तर छान्नुहोस्)",
                    "translated_text": "Who did they learn about at the Himalayan Mountaineering Institute? (Choose two answers)",
                    "options": ["तेन्जिङ नोर्गे", "एडमन्ड हिलारी", "जुङ्गो ताबेई", "रेनहोल्ड मेसनर"],
                    "answer_hint": "स्क्रिप्टमा उल्लेखित दुई व्यक्तित्व छान्नुहोस्",
                    "metadata": {}
                },
                "correct_answer": {
                    "value": ["तेन्जिङ नोर्गे", "एडमन्ड हिलारी"],  # LIST for multiple_choice
                    "type": "multiple_choice"
                },
                "user_answer": None,
                "status": "pending",
                "result": None,
                "submitted": False,
                "is_attempted": False,
                "attempts_count": 0,
                "total_score": 10,
                "scored": 0,
                "created_at": datetime.now(timezone.utc),
                "updated_at": datetime.now(timezone.utc),
                "metadata": {
                    "difficulty_level": 2,
                    "topic": "mountaineering_history",
                    "learning_objective": "historical_knowledge"
                }
            },
            {
                "task_set_id": set1_id,
                "user_id": USER_ID,
                "type": "single_choice",
                "title": "कुन ठाउँबाट कञ्चनजङ्घाको सूर्योदय हेरे?",
                "question": {
                    "text": "कुन ठाउँबाट कञ्चनजङ्घाको सूर्योदय हेरे?",
                    "translated_text": "From which place did they watch the sunrise over Kanchenjunga?",
                    "options": ["टाइगर हिल", "डार्जिलिङ मल", "हिमालयन इन्स्टिच्युट", "बाटासिया लुप"],
                    "answer_hint": "सूर्योदय हेर्ने प्रसिद्ध स्थान",
                    "metadata": {}
                },
                "correct_answer": {
                    "value": "टाइगर हिल",
                    "type": "single_choice"
                },
                "user_answer": None,
                "status": "pending",
                "result": None,
                "submitted": False,
                "is_attempted": False,
                "attempts_count": 0,
                "total_score": 10,
                "scored": 0,
                "created_at": datetime.now(timezone.utc),
                "updated_at": datetime.now(timezone.utc),
                "metadata": {
                    "difficulty_level": 2,
                    "topic": "tourism_geography",
                    "learning_objective": "landmark_identification"
                }
            },
            {
                "task_set_id": set1_id,
                "user_id": USER_ID,
                "type": "single_choice",
                "title": "भारतीय सीमा कुन ठाउँमा पार गरे?",
                "question": {
                    "text": "भारतीय सीमा कुन ठाउँमा पार गरे?",
                    "translated_text": "Where did they cross the Indian border?",
                    "options": ["काकडभिट्टा", "बिर्गञ्ज", "भैरहवा", "महेन्द्रनगर"],
                    "answer_hint": "पूर्वी सीमानाका प्रमुख नाका",
                    "metadata": {}
                },
                "correct_answer": {
                    "value": "काकडभिट्टा",
                    "type": "single_choice"
                },
                "user_answer": None,
                "status": "pending",
                "result": None,
                "submitted": False,
                "is_attempted": False,
                "attempts_count": 0,
                "total_score": 10,
                "scored": 0,
                "created_at": datetime.now(timezone.utc),
                "updated_at": datetime.now(timezone.utc),
                "metadata": {
                    "difficulty_level": 2,
                    "topic": "border_geography",
                    "learning_objective": "border_crossing_knowledge"
                }
            }
        ]
        
        # Insert all questions
        question_results = await db.curated_content_items.insert_many(questions_set1)
        question_ids = list(question_results.inserted_ids)
        
        # Update the curated content set with question IDs
        await db.curated_content_set.update_one(
            {"_id": set1_id},
            {"$set": {"tasks": [str(qid) for qid in question_ids]}}
        )
        
        print(f"Successfully created Theme 1: Educational Tours")
        print(f"Theme ID: {theme_id}")
        print(f"Set ID: {set1_id}")
        print(f"Questions inserted: {len(question_ids)}")
        print(f"Question types: single_choice: 5, multiple_choice: 1")
        
    except Exception as error:
        print(f"Error: {error}")
    finally:
        await client.close()

if __name__ == "__main__":
    asyncio.run(insert_theme1_correct_structure())
