import asyncio
from pymongo import AsyncMongoClient
from bson import ObjectId
import json

# Database configuration
MONGO_URI = "mongodb+srv://diwas:<EMAIL>/"
DB_NAME = "test_nepali_app"

async def check_existing_structure():
    """Check the existing structure of task_sets and task_items collections"""
    
    client = AsyncMongoClient(MONGO_URI)
    
    try:
        db = client[DB_NAME]
        
        print("=== CHECKING EXISTING COLLECTION STRUCTURES ===\n")
        
        # Check task_sets collection
        print("1. TASK_SETS COLLECTION:")
        task_sets_count = await db.task_sets.count_documents({})
        print(f"   Total documents: {task_sets_count}")
        
        if task_sets_count > 0:
            # Get one sample document
            sample_task_set = await db.task_sets.find_one({})
            print("   Sample document structure:")
            print("   " + "="*50)
            for key, value in sample_task_set.items():
                if key == "_id":
                    print(f"   {key}: ObjectId('{value}')")
                elif isinstance(value, list) and len(value) > 0:
                    print(f"   {key}: [array with {len(value)} items] - First item: {value[0] if value else 'None'}")
                elif isinstance(value, dict):
                    print(f"   {key}: {{dict with {len(value)} keys}} - Keys: {list(value.keys())}")
                else:
                    print(f"   {key}: {type(value).__name__} = {value}")
            print("   " + "="*50)
        
        print("\n2. TASK_ITEMS COLLECTION:")
        task_items_count = await db.task_items.count_documents({})
        print(f"   Total documents: {task_items_count}")
        
        if task_items_count > 0:
            # Get one sample document
            sample_task_item = await db.task_items.find_one({})
            print("   Sample document structure:")
            print("   " + "="*50)
            for key, value in sample_task_item.items():
                if key == "_id":
                    print(f"   {key}: ObjectId('{value}')")
                elif isinstance(value, list) and len(value) > 0:
                    print(f"   {key}: [array with {len(value)} items]")
                elif isinstance(value, dict):
                    print(f"   {key}: {{dict with {len(value)} keys}} - Keys: {list(value.keys())}")
                else:
                    print(f"   {key}: {type(value).__name__} = {value}")
            print("   " + "="*50)
        
        print("\n3. CURATED_CONTENT_SET COLLECTION:")
        curated_sets_count = await db.curated_content_set.count_documents({})
        print(f"   Total documents: {curated_sets_count}")
        
        if curated_sets_count > 0:
            # Get one sample document
            sample_curated_set = await db.curated_content_set.find_one({})
            print("   Sample document structure:")
            print("   " + "="*50)
            for key, value in sample_curated_set.items():
                if key == "_id":
                    print(f"   {key}: ObjectId('{value}')")
                elif isinstance(value, list) and len(value) > 0:
                    print(f"   {key}: [array with {len(value)} items]")
                elif isinstance(value, dict):
                    print(f"   {key}: {{dict with {len(value)} keys}} - Keys: {list(value.keys())}")
                else:
                    print(f"   {key}: {type(value).__name__} = {value}")
            print("   " + "="*50)
        
        print("\n4. CURATED_CONTENT_ITEMS COLLECTION:")
        curated_items_count = await db.curated_content_items.count_documents({})
        print(f"   Total documents: {curated_items_count}")
        
        if curated_items_count > 0:
            # Get one sample document
            sample_curated_item = await db.curated_content_items.find_one({})
            print("   Sample document structure:")
            print("   " + "="*50)
            for key, value in sample_curated_item.items():
                if key == "_id":
                    print(f"   {key}: ObjectId('{value}')")
                elif isinstance(value, list) and len(value) > 0:
                    print(f"   {key}: [array with {len(value)} items]")
                elif isinstance(value, dict):
                    print(f"   {key}: {{dict with {len(value)} keys}} - Keys: {list(value.keys())}")
                else:
                    print(f"   {key}: {type(value).__name__} = {value}")
            print("   " + "="*50)
        
        print("\n5. THEMES COLLECTION:")
        themes_count = await db.themes.count_documents({})
        print(f"   Total documents: {themes_count}")
        
        if themes_count > 0:
            # Get one sample document
            sample_theme = await db.themes.find_one({})
            print("   Sample document structure:")
            print("   " + "="*50)
            for key, value in sample_theme.items():
                if key == "_id":
                    print(f"   {key}: ObjectId('{value}')")
                elif isinstance(value, list) and len(value) > 0:
                    print(f"   {key}: [array with {len(value)} items]")
                elif isinstance(value, dict):
                    print(f"   {key}: {{dict with {len(value)} keys}} - Keys: {list(value.keys())}")
                else:
                    print(f"   {key}: {type(value).__name__} = {value}")
            print("   " + "="*50)
        
        print("\n=== ANALYSIS COMPLETE ===")
        
    except Exception as error:
        print(f"Error: {error}")
    finally:
        await client.close()

if __name__ == "__main__":
    asyncio.run(check_existing_structure())
