import asyncio
from datetime import datetime, timezone
from pymongo import AsyncMongoClient
from bson import ObjectId

# Database configuration
MONGO_URI = "mongodb+srv://diwas:<EMAIL>/"
DB_NAME = "test_nepali_app"
USER_ID = ObjectId('68391d86b8b0e7ec9ababfbb')

async def create_theme2_historical_sites():
    """Create Theme 2: Historical Sites Visit with 5 complete sets"""
    
    client = AsyncMongoClient(MONGO_URI)
    
    try:
        db = client[DB_NAME]
        
        # Insert Theme 2: Historical Sites Visit
        theme = {
            "name": "ऐतिहासिक स्थल भ्रमण",
            "name_en": "Historical Sites Visit", 
            "description": "नेपालका महत्वपूर्ण ऐतिहासिक स्थलहरूको भ्रमण र अध्ययन",
            "description_en": "Visit and study of important historical sites of Nepal",
            "order": 2,
            "is_active": True,
            "created_at": datetime.now(timezone.utc),
            "updated_at": datetime.now(timezone.utc),
            "category": "इतिहास",
            "category_en": "History",
            "color": "#8B4513",
            "icon": "🏛️"
        }
        
        theme_result = await db.themes.insert_one(theme)
        theme_id = theme_result.inserted_id
        print(f"Theme 2 inserted with ID: {theme_id}")
        
        # Create all 5 sets with complete questions
        all_sets_data = []
        
        # SET 1: Bhaktapur Durbar Square
        set1_data = {
            "set_info": {
                "user_id": USER_ID,  # ObjectId format
                "input_type": "text",
                "input_content": {
                    "script": "म सुनिता हुँ, काभ्रेको शान्ति माध्यमिक विद्यालयकी कक्षा ९ की विद्यार्थी। हाम्रो नेपाली इतिहासको कक्षाको लागि भक्तपुर दरबार स्क्वायर भ्रमण गर्यौं। त्यहाँ पुगेपछि गाइडले भन्यो कि यो १२औं शताब्दीमा निर्माण भएको थियो। ५५ झ्यालेको दरबार देखेर अचम्म लाग्यो। त्यहाँ न्यातपोल मन्दिर छ जुन ५ तले छ र नेपालकै अग्लो मन्दिर हो। गाइडले भन्यो कि यो मन्दिर राजा भूपतिन्द्र मल्लले बनाएका थिए। हामीले मल्लकालीन कलाकृतिहरू, काठका झ्यालहरू र ढुङ्गाका मूर्तिहरू देख्यौं। सबैभन्दा रोचक कुरा के थियो भने त्यहाँ अझै पनि कुमारी देवीको घर छ। भक्तपुरको कुमारी काठमाडौंको कुमारी भन्दा फरक परम्परा छ।",
                    "title": "भक्तपुर दरबार स्क्वायर ऐतिहासिक भ्रमण",
                    "title_en": "Bhaktapur Durbar Square Historical Visit",
                    "location": "भक्तपुर",
                    "character": "सुनिता",
                    "school": "शान्ति माध्यमिक विद्यालय",
                    "district": "काभ्रे"
                },
                "metadata": {
                    "theme_id": str(theme_id),
                    "script_type": "historical_tour",
                    "generated_by": "curated_content",
                    "content_category": "historical_heritage",
                    "target_audience": "grade_9_students",
                    "learning_objectives": ["historical_knowledge", "architectural_appreciation", "cultural_heritage"]
                }
            },
            "questions": [
                {
                    "type": "single_choice",
                    "title": "सुनिता कुन जिल्लाकी विद्यार्थी हिन्?",
                    "question": {
                        "text": "सुनिता कुन जिल्लाकी विद्यार्थी हिन्?",
                        "translated_text": "Which district is Sunita a student from?",
                        "options": ["काभ्रे", "भक्तपुर", "ललितपुर", "काठमाडौं"],
                        "answer_hint": "स्क्रिप्टको सुरुमा उल्लेख छ",
                        "metadata": {}
                    },
                    "correct_answer": {"value": "काभ्रे", "type": "single_choice"}
                },
                {
                    "type": "single_choice",
                    "title": "भक्तपुर दरबार स्क्वायर कुन शताब्दीमा निर्माण भएको थियो?",
                    "question": {
                        "text": "भक्तपुर दरबार स्क्वायर कुन शताब्दीमा निर्माण भएको थियो?",
                        "translated_text": "In which century was Bhaktapur Durbar Square built?",
                        "options": ["११औं", "१२औं", "१३औं", "१४औं"],
                        "answer_hint": "गाइडले बताएको समय",
                        "metadata": {}
                    },
                    "correct_answer": {"value": "१२औं", "type": "single_choice"}
                },
                {
                    "type": "single_choice",
                    "title": "५५ झ्यालेको दरबारमा कति वटा झ्याल छन्?",
                    "question": {
                        "text": "५५ झ्यालेको दरबारमा कति वटा झ्याल छन्?",
                        "translated_text": "How many windows are there in the 55-window palace?",
                        "options": ["५३", "५५", "५७", "५९"],
                        "answer_hint": "दरबारको नामबाटै थाहा हुन्छ",
                        "metadata": {}
                    },
                    "correct_answer": {"value": "५५", "type": "single_choice"}
                },
                {
                    "type": "single_choice",
                    "title": "न्यातपोल मन्दिर कति तले छ?",
                    "question": {
                        "text": "न्यातपोल मन्दिर कति तले छ?",
                        "translated_text": "How many stories does Nyatapola temple have?",
                        "options": ["३", "४", "५", "६"],
                        "answer_hint": "नेपालकै अग्लो मन्दिर",
                        "metadata": {}
                    },
                    "correct_answer": {"value": "५", "type": "single_choice"}
                },
                {
                    "type": "single_choice",
                    "title": "न्यातपोल मन्दिर कसले बनाएका थिए?",
                    "question": {
                        "text": "न्यातपोल मन्दिर कसले बनाएका थिए?",
                        "translated_text": "Who built the Nyatapola temple?",
                        "options": ["राजा भूपतिन्द्र मल्ल", "राजा प्रताप मल्ल", "राजा जयस्थिति मल्ल", "राजा यक्ष मल्ल"],
                        "answer_hint": "गाइडले बताएको मल्ल राजा",
                        "metadata": {}
                    },
                    "correct_answer": {"value": "राजा भूपतिन्द्र मल्ल", "type": "single_choice"}
                },
                {
                    "type": "multiple_choice",
                    "title": "भक्तपुरमा कस्ता कलाकृतिहरू देखे?",
                    "question": {
                        "text": "भक्तपुरमा कस्ता कलाकृतिहरू देखे? (तीनवटा छान्नुहोस्)",
                        "translated_text": "What kind of artworks did they see in Bhaktapur? (Choose three)",
                        "options": ["मल्लकालीन कलाकृति", "काठका झ्याल", "ढुङ्गाका मूर्ति", "आधुनिक चित्र"],
                        "answer_hint": "स्क्रिप्टमा उल्लेखित तीनवटा कलाकृति",
                        "metadata": {}
                    },
                    "correct_answer": {"value": ["मल्लकालीन कलाकृति", "काठका झ्याल", "ढुङ्गाका मूर्ति"], "type": "multiple_choice"}
                }
            ]
        }
        
        # SET 2: Lumbini Pilgrimage
        set2_data = {
            "set_info": {
                "user_id": USER_ID,
                "input_type": "text",
                "input_content": {
                    "script": "म राजेश हुँ, कपिलवस्तुको गौतम बुद्ध माध्यमिक विद्यालयको विद्यार्थी। हाम्रो बुद्ध धर्म र इतिहासको कक्षाको लागि लुम्बिनी तीर्थयात्रा गर्यौं। लुम्बिनी भगवान बुद्धको जन्मस्थान हो। त्यहाँ पुगेपछि पहिले हामी मायादेवी मन्दिर गयौं। गाइडले भन्यो कि यहीं नै राजकुमार सिद्धार्थको जन्म भएको थियो। त्यहाँ एउटा ढुङ्गामा खोपिएको चित्र छ जसमा मायादेवीले बच्चा जन्माएको देखाइएको छ। त्यसपछि हामी अशोक स्तम्भ देख्न गयौं। यो स्तम्भ सम्राट अशोकले ईसापूर्व २४९ मा राखेका थिए। त्यहाँ विभिन्न देशका मठहरू छन् - थाई मठ, म्यानमार मठ, श्रीलंकाली मठ। सबैभन्दा सुन्दर चिनियाँ मठ थियो। लुम्बिनी युनेस्कोको विश्व सम्पदा सूचीमा छ।",
                    "title": "लुम्बिनी बुद्ध जन्मस्थल तीर्थयात्रा",
                    "title_en": "Lumbini Buddha Birthplace Pilgrimage",
                    "location": "लुम्बिनी",
                    "character": "राजेश",
                    "school": "गौतम बुद्ध माध्यमिक विद्यालय",
                    "district": "कपिलवस्तु"
                },
                "metadata": {
                    "theme_id": str(theme_id),
                    "script_type": "historical_tour",
                    "generated_by": "curated_content",
                    "content_category": "religious_heritage",
                    "target_audience": "secondary_students",
                    "learning_objectives": ["buddhist_history", "religious_tolerance", "world_heritage"]
                }
            },
            "questions": [
                {
                    "type": "single_choice",
                    "title": "राजेश कुन जिल्लाको विद्यार्थी हो?",
                    "question": {
                        "text": "राजेश कुन जिल्लाको विद्यार्थी हो?",
                        "translated_text": "Which district is Rajesh a student from?",
                        "options": ["कपिलवस्तु", "रुपन्देही", "नवलपरासी", "दाङ"],
                        "answer_hint": "स्क्रिप्टको सुरुमा उल्लेख छ",
                        "metadata": {}
                    },
                    "correct_answer": {"value": "कपिलवस्तु", "type": "single_choice"}
                },
                {
                    "type": "single_choice",
                    "title": "लुम्बिनी कसको जन्मस्थान हो?",
                    "question": {
                        "text": "लुम्बिनी कसको जन्मस्थान हो?",
                        "translated_text": "Whose birthplace is Lumbini?",
                        "options": ["भगवान बुद्ध", "भगवान विष्णु", "भगवान शिव", "भगवान गणेश"],
                        "answer_hint": "राजकुमार सिद्धार्थ",
                        "metadata": {}
                    },
                    "correct_answer": {"value": "भगवान बुद्ध", "type": "single_choice"}
                },
                {
                    "type": "single_choice",
                    "title": "अशोक स्तम्भ कसले राखेका थिए?",
                    "question": {
                        "text": "अशोक स्तम्भ कसले राखेका थिए?",
                        "translated_text": "Who erected the Ashoka pillar?",
                        "options": ["सम्राट अशोक", "राजा जनक", "राजा दशरथ", "राजा शुद्धोधन"],
                        "answer_hint": "स्तम्भको नामबाटै थाहा हुन्छ",
                        "metadata": {}
                    },
                    "correct_answer": {"value": "सम्राट अशोक", "type": "single_choice"}
                },
                {
                    "type": "single_choice",
                    "title": "अशोक स्तम्भ कहिले राखिएको थियो?",
                    "question": {
                        "text": "अशोक स्तम्भ कहिले राखिएको थियो?",
                        "translated_text": "When was the Ashoka pillar erected?",
                        "options": ["ईसापूर्व २४७", "ईसापूर्व २४९", "ईसापूर्व २५१", "ईसापूर्व २५३"],
                        "answer_hint": "स्क्रिप्टमा उल्लेखित वर्ष",
                        "metadata": {}
                    },
                    "correct_answer": {"value": "ईसापूर्व २४९", "type": "single_choice"}
                },
                {
                    "type": "multiple_choice",
                    "title": "लुम्बिनीमा कुन कुन देशका मठहरू छन्?",
                    "question": {
                        "text": "लुम्बिनीमा कुन कुन देशका मठहरू छन्? (तीनवटा छान्नुहोस्)",
                        "translated_text": "Which countries' monasteries are there in Lumbini? (Choose three)",
                        "options": ["थाईल्यान्ड", "म्यानमार", "श्रीलंका", "भारत", "जापान", "कोरिया"],
                        "answer_hint": "स्क्रिप्टमा उल्लेखित तीनवटा देश",
                        "metadata": {}
                    },
                    "correct_answer": {"value": ["थाईल्यान्ड", "म्यानमार", "श्रीलंका"], "type": "multiple_choice"}
                },
                {
                    "type": "single_choice",
                    "title": "सबैभन्दा सुन्दर कुन देशको मठ थियो?",
                    "question": {
                        "text": "सबैभन्दा सुन्दर कुन देशको मठ थियो?",
                        "translated_text": "Which country's monastery was the most beautiful?",
                        "options": ["चीन", "जापान", "थाईल्यान्ड", "म्यानमार"],
                        "answer_hint": "स्क्रिप्टमा उल्लेखित सुन्दर मठ",
                        "metadata": {}
                    },
                    "correct_answer": {"value": "चीन", "type": "single_choice"}
                }
            ]
        }
        
        # SET 3: Gorkha Palace
        set3_data = {
            "set_info": {
                "user_id": USER_ID,
                "input_type": "text",
                "input_content": {
                    "script": "म प्रिया हुँ, गोरखाको प्रिथ्वी माध्यमिक विद्यालयकी विद्यार्थी। हाम्रो स्थानीय इतिहासको बारेमा जान्नको लागि गोरखा दरबार भ्रमण गर्यौं। यो दरबार राजा पृथ्वीनारायण शाहको जन्मस्थान हो। दरबार पहाडको टुप्पोमा छ र त्यहाँ पुग्न २ घण्टा हिड्नुपर्छ। गाइडले भन्यो कि यो दरबार १६औं शताब्दीमा निर्माण भएको थियो। त्यहाँबाट हिमालयको सुन्दर दृश्य देखिन्छ। दरबारमा पृथ्वीनारायण शाहको तस्बिर र उनका हतियारहरू छन्। त्यहाँ गोरखनाथको मन्दिर पनि छ। गाइडले भन्यो कि पृथ्वीनारायण शाहले नेपाल एकीकरण गर्नुअघि यहीं बसेर योजना बनाउनुहुन्थ्यो। दरबारबाट गोरखा बजार र त्रिशूली नदी देखिन्छ।",
                    "title": "गोरखा दरबार ऐतिहासिक भ्रमण",
                    "title_en": "Gorkha Palace Historical Visit",
                    "location": "गोरखा",
                    "character": "प्रिया",
                    "school": "प्रिथ्वी माध्यमिक विद्यालय",
                    "district": "गोरखा"
                },
                "metadata": {
                    "theme_id": str(theme_id),
                    "script_type": "historical_tour",
                    "generated_by": "curated_content"
                }
            },
            "questions": [
                {
                    "type": "single_choice",
                    "title": "प्रिया कुन जिल्लाकी विद्यार्थी हिन्?",
                    "question": {
                        "text": "प्रिया कुन जिल्लाकी विद्यार्थी हिन्?",
                        "translated_text": "Which district is Priya a student from?",
                        "options": ["गोरखा", "तनहुँ", "लमजुङ", "मनाङ"],
                        "answer_hint": "स्क्रिप्टको सुरुमा उल्लेख छ",
                        "metadata": {}
                    },
                    "correct_answer": {"value": "गोरखा", "type": "single_choice"}
                },
                {
                    "type": "single_choice",
                    "title": "गोरखा दरबार कसको जन्मस्थान हो?",
                    "question": {
                        "text": "गोरखा दरबार कसको जन्मस्थान हो?",
                        "translated_text": "Whose birthplace is Gorkha Palace?",
                        "options": ["राजा पृथ्वीनारायण शाह", "राजा महेन्द्र", "राजा त्रिभुवन", "राजा वीरेन्द्र"],
                        "answer_hint": "नेपाल एकीकरण गर्ने राजा",
                        "metadata": {}
                    },
                    "correct_answer": {"value": "राजा पृथ्वीनारायण शाह", "type": "single_choice"}
                },
                {
                    "type": "single_choice",
                    "title": "दरबार पुग्न कति घण्टा हिड्नुपर्छ?",
                    "question": {
                        "text": "दरबार पुग्न कति घण्टा हिड्नुपर्छ?",
                        "translated_text": "How many hours does it take to walk to the palace?",
                        "options": ["१ घण्टा", "२ घण्टा", "३ घण्टा", "४ घण्टा"],
                        "answer_hint": "स्क्रिप्टमा उल्लेखित समय",
                        "metadata": {}
                    },
                    "correct_answer": {"value": "२ घण्टा", "type": "single_choice"}
                },
                {
                    "type": "single_choice",
                    "title": "गोरखा दरबार कुन शताब्दीमा निर्माण भएको थियो?",
                    "question": {
                        "text": "गोरखा दरबार कुन शताब्दीमा निर्माण भएको थियो?",
                        "translated_text": "In which century was Gorkha Palace built?",
                        "options": ["१५औं", "१६औं", "१७औं", "१८औं"],
                        "answer_hint": "गाइडले बताएको समय",
                        "metadata": {}
                    },
                    "correct_answer": {"value": "१६औं", "type": "single_choice"}
                },
                {
                    "type": "single_choice",
                    "title": "दरबारमा कुन देवताको मन्दिर छ?",
                    "question": {
                        "text": "दरबारमा कुन देवताको मन्दिर छ?",
                        "translated_text": "Which deity's temple is in the palace?",
                        "options": ["गोरखनाथ", "शिव", "विष्णु", "गणेश"],
                        "answer_hint": "गोरखाको नामसँग जोडिएको देवता",
                        "metadata": {}
                    },
                    "correct_answer": {"value": "गोरखनाथ", "type": "single_choice"}
                },
                {
                    "type": "multiple_choice",
                    "title": "दरबारबाट के के देखिन्छ?",
                    "question": {
                        "text": "दरबारबाट के के देखिन्छ? (दुईवटा छान्नुहोस्)",
                        "translated_text": "What can be seen from the palace? (Choose two)",
                        "options": ["गोरखा बजार", "त्रिशूली नदी", "काठमाडौं उपत्यका", "पोखरा ताल"],
                        "answer_hint": "स्क्रिप्टमा उल्लेखित दुई ठाउँ",
                        "metadata": {}
                    },
                    "correct_answer": {"value": ["गोरखा बजार", "त्रिशूली नदी"], "type": "multiple_choice"}
                }
            ]
        }

        # SET 4: Patan Durbar Square
        set4_data = {
            "set_info": {
                "user_id": USER_ID,
                "input_type": "text",
                "input_content": {
                    "script": "म अनिल हुँ, ललितपुरको कलाकार माध्यमिक विद्यालयको विद्यार्थी। हाम्रो कला र संस्कृतिको कक्षाको लागि पाटन दरबार स्क्वायर भ्रमण गर्यौं। पाटन नेवारी कलाकृतिको केन्द्र हो। त्यहाँ पुगेपछि गाइडले भन्यो कि यो ३औं शताब्दीदेखि बसोबास भएको ठाउँ हो। पाटन दरबारमा धेरै मन्दिरहरू छन् - कृष्ण मन्दिर, भीमसेन मन्दिर, विश्वनाथ मन्दिर। कृष्ण मन्दिर ढुङ्गाले बनेको छ र यो १७औं शताब्दीमा निर्माण भएको थियो। त्यहाँ पाटन संग्रहालय छ जहाँ नेवारी कलाकृतिहरू राखिएका छन्। हामीले धातुका मूर्तिहरू, काठका कलाकृतिहरू र पुरातन सिक्काहरू देख्यौं। पाटनका कारीगरहरू आज पनि परम्परागत तरिकाले धातुका मूर्तिहरू बनाउँछन्।",
                    "title": "पाटन दरबार स्क्वायर कलाकृति अध्ययन",
                    "title_en": "Patan Durbar Square Art Study",
                    "location": "पाटन",
                    "character": "अनिल",
                    "school": "कलाकार माध्यमिक विद्यालय",
                    "district": "ललितपुर"
                },
                "metadata": {
                    "theme_id": str(theme_id),
                    "script_type": "historical_tour",
                    "generated_by": "curated_content"
                }
            },
            "questions": [
                {
                    "type": "single_choice",
                    "title": "अनिल कुन जिल्लाको विद्यार्थी हो?",
                    "question": {
                        "text": "अनिल कुन जिल्लाको विद्यार्थी हो?",
                        "translated_text": "Which district is Anil a student from?",
                        "options": ["ललितपुर", "काठमाडौं", "भक्तपुर", "काभ्रे"],
                        "answer_hint": "स्क्रिप्टको सुरुमा उल्लेख छ",
                        "metadata": {}
                    },
                    "correct_answer": {"value": "ललितपुर", "type": "single_choice"}
                },
                {
                    "type": "single_choice",
                    "title": "पाटन कुन कलाकृतिको केन्द्र हो?",
                    "question": {
                        "text": "पाटन कुन कलाकृतिको केन्द्र हो?",
                        "translated_text": "Patan is the center of which art?",
                        "options": ["नेवारी कलाकृति", "तिब्बती कलाकृति", "भारतीय कलाकृति", "चिनियाँ कलाकृति"],
                        "answer_hint": "स्थानीय कलाकृतिको प्रकार",
                        "metadata": {}
                    },
                    "correct_answer": {"value": "नेवारी कलाकृति", "type": "single_choice"}
                },
                {
                    "type": "single_choice",
                    "title": "पाटनमा कुन शताब्दीदेखि बसोबास भएको छ?",
                    "question": {
                        "text": "पाटनमा कुन शताब्दीदेखि बसोबास भएको छ?",
                        "translated_text": "Since which century has Patan been inhabited?",
                        "options": ["२रो", "३रो", "४थो", "५औं"],
                        "answer_hint": "गाइडले बताएको समय",
                        "metadata": {}
                    },
                    "correct_answer": {"value": "३रो", "type": "single_choice"}
                },
                {
                    "type": "multiple_choice",
                    "title": "पाटन दरबारमा कुन कुन मन्दिरहरू छन्?",
                    "question": {
                        "text": "पाटन दरबारमा कुन कुन मन्दिरहरू छन्? (तीनवटा छान्नुहोस्)",
                        "translated_text": "Which temples are there in Patan Durbar? (Choose three)",
                        "options": ["कृष्ण मन्दिर", "भीमसेन मन्दिर", "विश्वनाथ मन्दिर", "गणेश मन्दिर"],
                        "answer_hint": "स्क्रिप्टमा उल्लेखित तीनवटा मन्दिर",
                        "metadata": {}
                    },
                    "correct_answer": {"value": ["कृष्ण मन्दिर", "भीमसेन मन्दिर", "विश्वनाथ मन्दिर"], "type": "multiple_choice"}
                },
                {
                    "type": "single_choice",
                    "title": "कृष्ण मन्दिर कुन शताब्दीमा निर्माण भएको थियो?",
                    "question": {
                        "text": "कृष्ण मन्दिर कुन शताब्दीमा निर्माण भएको थियो?",
                        "translated_text": "In which century was Krishna temple built?",
                        "options": ["१६औं", "१७औं", "१८औं", "१९औं"],
                        "answer_hint": "स्क्रिप्टमा उल्लेखित समय",
                        "metadata": {}
                    },
                    "correct_answer": {"value": "१७औं", "type": "single_choice"}
                },
                {
                    "type": "multiple_choice",
                    "title": "पाटन संग्रहालयमा कस्ता कलाकृतिहरू देखे?",
                    "question": {
                        "text": "पाटन संग्रहालयमा कस्ता कलाकृतिहरू देखे? (तीनवटा छान्नुहोस्)",
                        "translated_text": "What kind of artworks did they see in Patan Museum? (Choose three)",
                        "options": ["धातुका मूर्ति", "काठका कलाकृति", "पुरातन सिक्का", "आधुनिक चित्र"],
                        "answer_hint": "स्क्रिप्टमा उल्लेखित तीनवटा कलाकृति",
                        "metadata": {}
                    },
                    "correct_answer": {"value": ["धातुका मूर्ति", "काठका कलाकृति", "पुरातन सिक्का"], "type": "multiple_choice"}
                }
            ]
        }

        # SET 5: Hanuman Dhoka Palace
        set5_data = {
            "set_info": {
                "user_id": USER_ID,
                "input_type": "text",
                "input_content": {
                    "script": "म सुमन हुँ, काठमाडौंको त्रिभुवन माध्यमिक विद्यालयको विद्यार्थी। हाम्रो नेपाली इतिहासको कक्षाको लागि हनुमानढोका दरबार भ्रमण गर्यौं। यो काठमाडौंको पुरानो राजदरबार हो। त्यहाँ पुगेपछि गाइडले भन्यो कि यो दरबार मल्लकालदेखि शाहकालसम्म प्रयोग भएको थियो। दरबारको मुख्य ढोकामा हनुमानको मूर्ति छ, त्यसैले यसलाई हनुमानढोका भनिन्छ। त्यहाँ नौ तलेको बसन्तपुर टावर छ जुन नेपालकै अग्लो काठको संरचना हो। हामीले त्रिभुवन संग्रहालय देख्यौं जहाँ राजाहरूका तस्बिरहरू, मुकुटहरू र राजकीय पोशाकहरू छन्। दरबारमा कालभैरवको ठूलो मूर्ति छ। गाइडले भन्यो कि पहिले यहाँ झुटो बोल्ने मान्छेले सत्य बोल्नुपर्ने हुन्थ्यो।",
                    "title": "हनुमानढोका दरबार राजकीय इतिहास",
                    "title_en": "Hanuman Dhoka Palace Royal History",
                    "location": "काठमाडौं",
                    "character": "सुमन",
                    "school": "त्रिभुवन माध्यमिक विद्यालय",
                    "district": "काठमाडौं"
                },
                "metadata": {
                    "theme_id": str(theme_id),
                    "script_type": "historical_tour",
                    "generated_by": "curated_content"
                }
            },
            "questions": [
                {
                    "type": "single_choice",
                    "title": "सुमन कुन जिल्लाको विद्यार्थी हो?",
                    "question": {
                        "text": "सुमन कुन जिल्लाको विद्यार्थी हो?",
                        "translated_text": "Which district is Suman a student from?",
                        "options": ["काठमाडौं", "ललितपुर", "भक्तपुर", "काभ्रे"],
                        "answer_hint": "स्क्रिप्टको सुरुमा उल्लेख छ",
                        "metadata": {}
                    },
                    "correct_answer": {"value": "काठमाडौं", "type": "single_choice"}
                },
                {
                    "type": "single_choice",
                    "title": "हनुमानढोका दरबार कुन कालदेखि कुन कालसम्म प्रयोग भएको थियो?",
                    "question": {
                        "text": "हनुमानढोका दरबार कुन कालदेखि कुन कालसम्म प्रयोग भएको थियो?",
                        "translated_text": "From which period to which period was Hanuman Dhoka palace used?",
                        "options": ["मल्लकालदेखि शाहकालसम्म", "लिच्छविकालदेखि मल्लकालसम्म", "शाहकालदेखि राणाकालसम्म", "राणाकालदेखि आजसम्म"],
                        "answer_hint": "गाइडले बताएको समयावधि",
                        "metadata": {}
                    },
                    "correct_answer": {"value": "मल्लकालदेखि शाहकालसम्म", "type": "single_choice"}
                },
                {
                    "type": "single_choice",
                    "title": "दरबारको नाम हनुमानढोका किन राखिएको हो?",
                    "question": {
                        "text": "दरबारको नाम हनुमानढोका किन राखिएको हो?",
                        "translated_text": "Why is the palace named Hanuman Dhoka?",
                        "options": ["मुख्य ढोकामा हनुमानको मूर्ति छ", "हनुमान राजाको नाम थियो", "हनुमान चोकमा छ", "हनुमानले बनाएको थियो"],
                        "answer_hint": "मुख्य ढोकासँग सम्बन्धित",
                        "metadata": {}
                    },
                    "correct_answer": {"value": "मुख्य ढोकामा हनुमानको मूर्ति छ", "type": "single_choice"}
                },
                {
                    "type": "single_choice",
                    "title": "बसन्तपुर टावर कति तलेको छ?",
                    "question": {
                        "text": "बसन्तपुर टावर कति तलेको छ?",
                        "translated_text": "How many stories does Basantapur Tower have?",
                        "options": ["७", "८", "९", "१०"],
                        "answer_hint": "नेपालकै अग्लो काठको संरचना",
                        "metadata": {}
                    },
                    "correct_answer": {"value": "९", "type": "single_choice"}
                },
                {
                    "type": "multiple_choice",
                    "title": "त्रिभुवन संग्रहालयमा के के छन्?",
                    "question": {
                        "text": "त्रिभुवन संग्रहालयमा के के छन्? (तीनवटा छान्नुहोस्)",
                        "translated_text": "What are there in Tribhuvan Museum? (Choose three)",
                        "options": ["राजाहरूका तस्बिर", "मुकुट", "राजकीय पोशाक", "आधुनिक कपडा"],
                        "answer_hint": "स्क्रिप्टमा उल्लेखित तीनवटा वस्तु",
                        "metadata": {}
                    },
                    "correct_answer": {"value": ["राजाहरूका तस्बिर", "मुकुट", "राजकीय पोशाक"], "type": "multiple_choice"}
                },
                {
                    "type": "single_choice",
                    "title": "दरबारमा कुन देवताको ठूलो मूर्ति छ?",
                    "question": {
                        "text": "दरबारमा कुन देवताको ठूलो मूर्ति छ?",
                        "translated_text": "Which deity's large statue is in the palace?",
                        "options": ["कालभैरव", "गणेश", "शिव", "विष्णु"],
                        "answer_hint": "स्क्रिप्टमा उल्लेखित देवता",
                        "metadata": {}
                    },
                    "correct_answer": {"value": "कालभैरव", "type": "single_choice"}
                }
            ]
        }

        all_sets_data = [set1_data, set2_data, set3_data, set4_data, set5_data]
        
        # Process each set
        for set_data in all_sets_data:
            # Insert the curated content set
            set_info = set_data["set_info"]
            set_info.update({
                "input_data": None,
                "tasks": [],
                "created_at": datetime.now(timezone.utc),
                "submitted_at": None,
                "verified_at": None,
                "completed_at": None,
                "status": "pending",
                "difficulty": "medium",
                "total_tasks": len(set_data["questions"]),
                "attempted_tasks": 0,
                "total_score": len(set_data["questions"]) * 10,
                "scored": 0,
                "notes": None,
                "remark": None
            })
            
            set_result = await db.curated_content_set.insert_one(set_info)
            set_id = set_result.inserted_id
            
            # Insert questions for this set
            questions = []
            for q_data in set_data["questions"]:
                question = {
                    "task_set_id": set_id,
                    "user_id": USER_ID,
                    "type": q_data["type"],
                    "title": q_data["title"],
                    "question": q_data["question"],
                    "correct_answer": q_data["correct_answer"],
                    "user_answer": None,
                    "status": "pending",
                    "result": None,
                    "submitted": False,
                    "is_attempted": False,
                    "attempts_count": 0,
                    "total_score": 10,
                    "scored": 0,
                    "created_at": datetime.now(timezone.utc),
                    "updated_at": datetime.now(timezone.utc),
                    "metadata": {"difficulty_level": 1, "topic": "historical_comprehension"}
                }
                questions.append(question)
            
            # Insert all questions
            question_results = await db.curated_content_items.insert_many(questions)
            question_ids = list(question_results.inserted_ids)
            
            # Update set with question IDs
            await db.curated_content_set.update_one(
                {"_id": set_id},
                {"$set": {"tasks": [str(qid) for qid in question_ids]}}
            )
            
            print(f"Set '{set_info['input_content']['title']}' created with {len(question_ids)} questions")
        
        print(f"\nTheme 2 (partial) completed successfully!")
        print(f"Theme ID: {theme_id}")
        print(f"Sets created so far: {len(all_sets_data)}")
        print("Note: Need to add remaining 3 sets to complete Theme 2")
        
    except Exception as error:
        print(f"Error: {error}")
    finally:
        await client.close()

if __name__ == "__main__":
    asyncio.run(create_theme2_historical_sites())
