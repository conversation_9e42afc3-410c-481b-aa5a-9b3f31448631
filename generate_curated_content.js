const { MongoClient, ObjectId } = require('mongodb');

const uri = 'mongodb://localhost:27017';
const dbName = 'test_nepali_app';
const userId = new ObjectId('68391d86b8b0e7ec9ababfbb');

async function insertCuratedContent() {
  const client = new MongoClient(uri);
  
  try {
    await client.connect();
    const db = client.db(dbName);
    
    // Category 1: History & Education
    const historyTheme = {
      name: "शैक्षिक भ्रमण",
      name_en: "Educational Tours", 
      description: "विभिन्न शैक्षिक स्थानहरूको भ्रमण र सिकाइ",
      description_en: "Educational visits and learning at various academic sites",
      order: 1,
      is_active: true,
      created_at: new Date(),
      updated_at: new Date(),
      category: "इतिहास",
      category_en: "History",
      color: "#8B4513",
      icon: "🎓"
    };
    
    const themeResult = await db.collection('themes').insertOne(historyTheme);
    const themeId = themeResult.insertedId;
    
    // Curated Content Sets
    const curatedSets = [
      {
        title: "दार्जिलिङ स्कूल भ्रमण",
        title_en: "Darjeeling School Tour",
        script: "म दिवास हुँ, रौतहट जिल्लाको गरुडा माध्यमिक विद्यालयमा कक्षा ९ मा पढ्छु। हाम्रो स्कूलले पहिलो पटक दार्जिलिङको शैक्षिक भ्रमणको आयोजना गर्यो। बिहान ५ बजे स्कूलबाट बस छुट्यो। हामी ३५ जना विद्यार्थी र ३ जना शिक्षक थियौं। पहिले हामी काकडभिट्टा पुग्यौं, त्यहाँ भारतीय सीमा पार गर्यौं। सिलिगुडी हुँदै दार्जिलिङ पुग्न ६ घण्टा लाग्यो। पहाडी बाटोमा घुम्ती सडकहरू देखेर धेरै रोमाञ्चक लाग्यो। दार्जिलिङ पुगेपछि हामीले हिमालयन माउन्टेनियरिङ इन्स्टिच्युट भ्रमण गर्यौं। त्यहाँ तेन्जिङ नोर्गे र एडमन्ड हिलारीको बारेमा जान्यौं। भोलिपल्ट बिहान टाइगर हिलबाट कञ्चनजङ्घाको सूर्योदय हेर्यौं। त्यो दृश्य अविस्मरणीय थियो।",
        theme_id: themeId,
        user_id: userId,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        title: "काठमाडौं संग्रहालय भ्रमण", 
        title_en: "Kathmandu Museum Visit",
        script: "म सरिता हुँ, धनकुटाको सरस्वती माध्यमिक विद्यालयकी विद्यार्थी। हाम्रो कक्षा १० को इतिहास विषयको लागि काठमाडौंका संग्रहालयहरूको भ्रमण गर्यौं। पहिले हामी राष्ट्रिय संग्रहालय पुग्यौं। त्यहाँ पुरातन हतियारहरू, राजाहरूका तस्बिरहरू र ऐतिहासिक कलाकृतिहरू देख्यौं। गाइडले भन्यो कि यो संग्रहालय १९२८ मा स्थापना भएको थियो। त्यसपछि हामी पाटन संग्रहालय गयौं। त्यहाँ नेवारी कलाकृति र धातुका मूर्तिहरू थिए। सबैभन्दा रोचक कुरा के थियो भने त्यहाँ ११औं शताब्दीका मूर्तिहरू थिए। अन्तमा हामी हनुमानढोकाको दरबार संग्रहालय गयौं। त्यहाँ मल्लकालीन र शाहकालीन राजाहरूका सामानहरू थिए।",
        theme_id: themeId,
        user_id: userId,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        title: "भक्तपुर दरबार स्क्वायर भ्रमण",
        title_en: "Bhaktapur Durbar Square Visit", 
        script: "म राम हुँ, काभ्रेको शान्ति माध्यमिक विद्यालयको विद्यार्थी। हाम्रो नेपाली इतिहासको कक्षाको लागि भक्तपुर दरबार स्क्वायर भ्रमण गर्यौं। त्यहाँ पुगेपछि गाइडले भन्यो कि यो १२औं शताब्दीमा निर्माण भएको थियो। ५५ झ्यालेको दरबार देखेर अचम्म लाग्यो। त्यहाँ न्यातपोल मन्दिर छ जुन ५ तले छ र नेपालकै अग्लो मन्दिर हो। गाइडले भन्यो कि यो मन्दिर राजा भूपतिन्द्र मल्लले बनाएका थिए। हामीले मल्लकालीन कलाकृतिहरू, काठका झ्यालहरू र ढुङ्गाका मूर्तिहरू देख्यौं। सबैभन्दा रोचक कुरा के थियो भने त्यहाँ अझै पनि कुमारी देवीको घर छ। भक्तपुरको कुमारी काठमाडौंको कुमारी भन्दा फरक परम्परा छ।",
        theme_id: themeId,
        user_id: userId,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        title: "लुम्बिनी तीर्थयात्रा",
        title_en: "Lumbini Pilgrimage",
        script: "म गीता हुँ, कपिलवस्तुकी विद्यार्थी। हाम्रो स्कूलले बुद्ध जयन्तीको अवसरमा लुम्बिनी भ्रमणको आयोजना गर्यो। लुम्बिनी भगवान बुद्धको जन्मस्थान हो। त्यहाँ पुगेपछि पहिले हामी मायादेवी मन्दिर गयौं। गाइडले भन्यो कि यहीं नै राजकुमार सिद्धार्थको जन्म भएको थियो। त्यहाँ एउटा ढुङ्गामा खोपिएको चित्र छ जसमा मायादेवीले बच्चा जन्माएको देखाइएको छ। त्यसपछि हामी अशोक स्तम्भ देख्न गयौं। यो स्तम्भ सम्राट अशोकले ईसापूर्व २४९ मा राखेका थिए। त्यहाँ विभिन्न देशका मठहरू छन् - थाई मठ, म्यानमार मठ, श्रीलंकाली मठ। सबैभन्दा सुन्दर चिनियाँ मठ थियो। लुम्बिनी विश्व सम्पदा सूचीमा पर्छ।",
        theme_id: themeId,
        user_id: userId,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        title: "गोरखा दरबार ऐतिहासिक भ्रमण",
        title_en: "Gorkha Palace Historical Visit",
        script: "म अर्जुन हुँ, गोरखाको प्रिथ्वी माध्यमिक विद्यालयको विद्यार्थी। हाम्रो स्थानीय इतिहासको बारेमा जान्नको लागि गोरखा दरबार भ्रमण गर्यौं। यो दरबार राजा पृथ्वीनारायण शाहको जन्मस्थान हो। दरबार पहाडको टुप्पोमा छ र त्यहाँ पुग्न २ घण्टा हिड्नुपर्छ। गाइडले भन्यो कि यो दरबार १६औं शताब्दीमा निर्माण भएको थियो। त्यहाँबाट हिमालयको सुन्दर दृश्य देखिन्छ। दरबारमा पृथ्वीनारायण शाहको तस्बिर र उनका हतियारहरू छन्। त्यहाँ गोरखनाथको मन्दिर पनि छ। गाइडले भन्यो कि पृथ्वीनारायण शाहले नेपाल एकीकरण गर्नुअघि यहीं बसेर योजना बनाउनुहुन्थ्यो। दरबारबाट गोरखा बजार र त्रिशूली नदी देखिन्छ।",
        theme_id: themeId,
        user_id: userId,
        created_at: new Date(),
        updated_at: new Date()
      }
    ];
    
    const setResults = await db.collection('curated_content_set').insertMany(curatedSets);
    console.log('Inserted curated content sets:', setResults.insertedIds);
    
    // Now insert questions for each set
    const questions = [];
    
    // Questions for Set 1: Darjeeling Tour
    const set1Id = setResults.insertedIds[0];
    questions.push(
      {
        type: "single_choice",
        title: "दिवास कुन जिल्लाको विद्यार्थी हो?",
        question: {
          type: "text",
          text: "दिवास कुन जिल्लाको विद्यार्थी हो?",
          translated_text: "Which district is Diwas a student from?",
          options: ["रौतहट", "सिरहा", "महोत्तरी", "सप्तरी"],
          answer_hint: "स्क्रिप्टमा उल्लेख गरिएको जिल्लाको नाम हेर्नुहोस्"
        },
        correct_answer: {
          type: "single",
          value: "रौतहट"
        },
        task_set_id: set1Id,
        user_id: userId,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        type: "single_choice", 
        title: "भ्रमणमा कति जना विद्यार्थी सहभागी थिए?",
        question: {
          type: "text",
          text: "भ्रमणमा कति जना विद्यार्थी सहभागी थिए?",
          translated_text: "How many students participated in the tour?",
          options: ["३०", "३५", "४०", "२५"],
          answer_hint: "स्क्रिप्टमा उल्लेखित विद्यार्थी संख्या हेर्नुहोस्"
        },
        correct_answer: {
          type: "single",
          value: "३५"
        },
        task_set_id: set1Id,
        user_id: userId,
        created_at: new Date(),
        updated_at: new Date()
      }
    );
    
    const questionResults = await db.collection('curated_content_items').insertMany(questions);
    console.log('Inserted questions:', questionResults.insertedIds.length);
    
    console.log('Successfully created Category 1: History & Education');
    console.log('Theme ID:', themeId);
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await client.close();
  }
}

insertCuratedContent();
