import asyncio
from datetime import datetime, timezone
from pymongo import AsyncMongoClient
from bson import ObjectId

# Database configuration
MONGO_URI = "mongodb+srv://diwas:<EMAIL>/"
DB_NAME = "test_nepali_app"
USER_ID = ObjectId('68391d86b8b0e7ec9ababfbb')

async def complete_theme1_remaining_sets():
    """Add remaining 4 sets to complete Theme 1: Educational Tours"""
    
    client = AsyncMongoClient(MONGO_URI)
    
    try:
        db = client[DB_NAME]
        
        # Get the existing theme ID
        theme = await db.themes.find_one({"name": "शैक्षिक भ्रमण"})
        if not theme:
            print("Theme not found!")
            return
        
        theme_id = theme["_id"]
        print(f"Found theme ID: {theme_id}")
        
        # Create remaining 4 sets
        remaining_sets = []
        
        # Set 2: Kathmandu Science Museum
        set2 = {
            "user_id": str(USER_ID),
            "input_type": "text",
            "input_content": {
                "script": "म सरिता हुँ, धनकुटाको सरस्वती माध्यमिक विद्यालयकी कक्षा १० की विद्यार्थी। हाम्रो विज्ञान विषयको लागि काठमाडौंको राष्ट्रिय विज्ञान संग्रहालय भ्रमण गर्यौं। त्यहाँ पुगेपछि पहिले हामी भौतिक विज्ञानको खण्डमा गयौं। त्यहाँ बिजुलीको प्रयोग, चुम्बकत्व र प्रकाशका बारेमा प्रयोगहरू थिए। सबैभन्दा रोचक कुरा के थियो भने त्यहाँ भ्यान डे ग्राफ जेनेरेटर थियो जसले स्थिर बिजुली बनाउँछ। त्यसपछि हामी रसायन विज्ञानको खण्डमा गयौं। त्यहाँ आवर्त सारणी र विभिन्न तत्वहरूका नमूनाहरू थिए। जीवविज्ञानको खण्डमा मानव शरीरका अंगहरूको मोडेल र डायनासोरका हड्डीहरू थिए। अन्तमा हामी तारामण्डलमा गयौं र ग्रह-नक्षत्रहरूको बारेमा जान्यौं।",
                "title": "काठमाडौं विज्ञान संग्रहालय भ्रमण",
                "title_en": "Kathmandu Science Museum Visit",
                "location": "काठमाडौं",
                "character": "सरिता",
                "school": "सरस्वती माध्यमिक विद्यालय",
                "district": "धनकुटा"
            },
            "input_data": None,
            "tasks": [],
            "created_at": datetime.now(timezone.utc),
            "submitted_at": None,
            "verified_at": None,
            "completed_at": None,
            "status": "pending",
            "difficulty": "medium",
            "total_tasks": 6,
            "attempted_tasks": 0,
            "total_score": 60,
            "scored": 0,
            "notes": None,
            "remark": None,
            "metadata": {
                "theme_id": str(theme_id),
                "script_type": "educational_tour",
                "generated_by": "curated_content",
                "content_category": "science_education",
                "target_audience": "grade_10_students",
                "learning_objectives": ["science_knowledge", "museum_experience", "practical_learning"]
            }
        }
        
        # Set 3: Pokhara Mountain Museum
        set3 = {
            "user_id": str(USER_ID),
            "input_type": "text",
            "input_content": {
                "script": "म राम हुँ, कास्कीको आदर्श माध्यमिक विद्यालयको विद्यार्थी। हाम्रो भूगोल कक्षाको लागि पोखराको अन्तर्राष्ट्रिय पर्वतारोहण संग्रहालय भ्रमण गर्यौं। यो संग्रहालय २००४ मा स्थापना भएको थियो। त्यहाँ पुगेपछि गाइडले नेपालका अष्टसहस्री पर्वतहरूको बारेमा बताए। एभरेस्ट, कञ्चनजङ्घा, ल्होत्से, मकालु, चो ओयु, धौलागिरी, मनास्लु र अन्नपूर्णा - यी सबै नेपालमा छन्। त्यहाँ पर्वतारोहीहरूले प्रयोग गर्ने उपकरणहरू थिए - रस्सी, बुट, अक्सिजन मास्क, टेन्ट। सबैभन्दा रोचक कुरा के थियो भने त्यहाँ शेर्पाहरूको संस्कृति र परम्पराको बारेमा जानकारी थियो। संग्रहालयबाट मछापुच्छ्रे र अन्नपूर्णा हिमालको सुन्दर दृश्य देखिन्थ्यो।",
                "title": "पोखरा अन्तर्राष्ट्रिय पर्वतारोहण संग्रहालय भ्रमण",
                "title_en": "Pokhara International Mountain Museum Visit",
                "location": "पोखरा",
                "character": "राम",
                "school": "आदर्श माध्यमिक विद्यालय",
                "district": "कास्की"
            },
            "input_data": None,
            "tasks": [],
            "created_at": datetime.now(timezone.utc),
            "submitted_at": None,
            "verified_at": None,
            "completed_at": None,
            "status": "pending",
            "difficulty": "medium",
            "total_tasks": 6,
            "attempted_tasks": 0,
            "total_score": 60,
            "scored": 0,
            "notes": None,
            "remark": None,
            "metadata": {
                "theme_id": str(theme_id),
                "script_type": "educational_tour",
                "generated_by": "curated_content",
                "content_category": "geography_education",
                "target_audience": "secondary_students",
                "learning_objectives": ["mountain_geography", "cultural_knowledge", "tourism_awareness"]
            }
        }
        
        # Set 4: Janakpur Cultural Study
        set4 = {
            "user_id": str(USER_ID),
            "input_type": "text",
            "input_content": {
                "script": "म गीता हुँ, धनुषाको राम जानकी माध्यमिक विद्यालयकी विद्यार्थी। हाम्रो नेपाली साहित्य र संस्कृतिको कक्षाको लागि जनकपुरको शैक्षिक भ्रमण गर्यौं। जनकपुर सीताको जन्मस्थान हो र यहाँ रामायणको कथा जोडिएको छ। पहिले हामी जानकी मन्दिर गयौं। यो मन्दिर १९११ मा निर्माण भएको थियो र यो राजस्थानी शैलीमा बनेको छ। त्यहाँ सीता र रामको मूर्ति छ। गाइडले भन्यो कि यहाँ हरेक वर्ष विवाह पञ्चमीमा ठूलो मेला लाग्छ। त्यसपछि हामी रामसागर तालमा गयौं। यो ताल राजा जनकले बनाएका थिए। त्यहाँ धेरै मन्दिरहरू छन्। जनकपुरमा मिथिला कलाको परम्परा छ। महिलाहरूले घरका भित्ताहरूमा सुन्दर चित्रहरू कोर्छन्।",
                "title": "जनकपुर जानकी मन्दिर र संस्कृति अध्ययन",
                "title_en": "Janakpur Janaki Temple and Cultural Study",
                "location": "जनकपुर",
                "character": "गीता",
                "school": "राम जानकी माध्यमिक विद्यालय",
                "district": "धनुषा"
            },
            "input_data": None,
            "tasks": [],
            "created_at": datetime.now(timezone.utc),
            "submitted_at": None,
            "verified_at": None,
            "completed_at": None,
            "status": "pending",
            "difficulty": "medium",
            "total_tasks": 6,
            "attempted_tasks": 0,
            "total_score": 60,
            "scored": 0,
            "notes": None,
            "remark": None,
            "metadata": {
                "theme_id": str(theme_id),
                "script_type": "educational_tour",
                "generated_by": "curated_content",
                "content_category": "cultural_education",
                "target_audience": "literature_students",
                "learning_objectives": ["cultural_heritage", "religious_knowledge", "art_appreciation"]
            }
        }
        
        # Set 5: Chitwan National Park
        set5 = {
            "user_id": str(USER_ID),
            "input_type": "text",
            "input_content": {
                "script": "म अर्जुन हुँ, चितवनको शहीद स्मृति माध्यमिक विद्यालयको विद्यार्थी। हाम्रो वातावरण विज्ञानको कक्षाको लागि चितवन राष्ट्रिय निकुञ्जको भ्रमण गर्यौं। यो निकुञ्ज १९७३ मा स्थापना भएको थियो र नेपालको पहिलो राष्ट्रिय निकुञ्ज हो। त्यहाँ पुगेपछि पहिले हामी निकुञ्जको मुख्यालयमा गयौं र गाइडले एक सींगे गैंडाको बारेमा बताए। नेपालमा करिब ७०० वटा गैंडा छन् र धेरैजसो चितवनमा छन्। त्यसपछि हामी जीप सफारीमा गयौं। त्यहाँ हामीले गैंडा, हात्ती, चितुवा र धेरै चराहरू देख्यौं। राप्ती नदीमा घडियाल र मगरमच्छ देख्यौं। गाइडले भन्यो कि यो निकुञ्ज युनेस्कोको विश्व सम्पदा सूचीमा छ। त्यहाँ थारु समुदायको संस्कृति पनि छ।",
                "title": "चितवन राष्ट्रिय निकुञ्ज शैक्षिक भ्रमण",
                "title_en": "Chitwan National Park Educational Visit",
                "location": "चितवन",
                "character": "अर्जुन",
                "school": "शहीद स्मृति माध्यमिक विद्यालय",
                "district": "चितवन"
            },
            "input_data": None,
            "tasks": [],
            "created_at": datetime.now(timezone.utc),
            "submitted_at": None,
            "verified_at": None,
            "completed_at": None,
            "status": "pending",
            "difficulty": "medium",
            "total_tasks": 6,
            "attempted_tasks": 0,
            "total_score": 60,
            "scored": 0,
            "notes": None,
            "remark": None,
            "metadata": {
                "theme_id": str(theme_id),
                "script_type": "educational_tour",
                "generated_by": "curated_content",
                "content_category": "environmental_education",
                "target_audience": "environmental_science_students",
                "learning_objectives": ["wildlife_conservation", "biodiversity_awareness", "ecosystem_understanding"]
            }
        }
        
        remaining_sets = [set2, set3, set4, set5]
        
        # Insert all remaining sets
        set_results = await db.curated_content_set.insert_many(remaining_sets)
        set_ids = list(set_results.inserted_ids)
        print(f"Inserted {len(set_ids)} additional curated content sets")
        
        # Create sample questions for Set 2 (Science Museum)
        set2_id = set_ids[0]
        questions_set2 = [
            {
                "task_set_id": set2_id,
                "user_id": USER_ID,
                "type": "single_choice",
                "title": "सरिता कुन जिल्लाकी विद्यार्थी हिन्?",
                "question": {
                    "text": "सरिता कुन जिल्लाकी विद्यार्थी हिन्?",
                    "translated_text": "Which district is Sarita a student from?",
                    "options": ["धनकुटा", "इलाम", "तेह्रथुम", "संखुवासभा"],
                    "answer_hint": "स्क्रिप्टको सुरुमा उल्लेख छ",
                    "metadata": {}
                },
                "correct_answer": {
                    "value": "धनकुटा",
                    "type": "single_choice"
                },
                "user_answer": None,
                "status": "pending",
                "result": None,
                "submitted": False,
                "is_attempted": False,
                "attempts_count": 0,
                "total_score": 10,
                "scored": 0,
                "created_at": datetime.now(timezone.utc),
                "updated_at": datetime.now(timezone.utc),
                "metadata": {
                    "difficulty_level": 1,
                    "topic": "geography",
                    "learning_objective": "location_identification"
                }
            },
            {
                "task_set_id": set2_id,
                "user_id": USER_ID,
                "type": "multiple_choice",
                "title": "विज्ञान संग्रहालयमा कुन कुन खण्डहरू थिए?",
                "question": {
                    "text": "विज्ञान संग्रहालयमा कुन कुन खण्डहरू थिए? (तीनवटा छान्नुहोस्)",
                    "translated_text": "Which sections were there in the science museum? (Choose three)",
                    "options": ["भौतिक विज्ञान", "रसायन विज्ञान", "जीवविज्ञान", "गणित"],
                    "answer_hint": "स्क्रिप्टमा उल्लेखित तीनवटा खण्डहरू",
                    "metadata": {}
                },
                "correct_answer": {
                    "value": ["भौतिक विज्ञान", "रसायन विज्ञान", "जीवविज्ञान"],
                    "type": "multiple_choice"
                },
                "user_answer": None,
                "status": "pending",
                "result": None,
                "submitted": False,
                "is_attempted": False,
                "attempts_count": 0,
                "total_score": 10,
                "scored": 0,
                "created_at": datetime.now(timezone.utc),
                "updated_at": datetime.now(timezone.utc),
                "metadata": {
                    "difficulty_level": 2,
                    "topic": "science_education",
                    "learning_objective": "categorization_skills"
                }
            }
        ]
        
        # Insert questions for Set 2
        question_results = await db.curated_content_items.insert_many(questions_set2)
        question_ids = list(question_results.inserted_ids)
        
        # Update Set 2 with question IDs
        await db.curated_content_set.update_one(
            {"_id": set2_id},
            {"$set": {"tasks": [str(qid) for qid in question_ids], "total_tasks": len(question_ids)}}
        )
        
        print(f"Successfully completed Theme 1: Educational Tours")
        print(f"Total sets in theme: 5")
        print(f"Sample questions added to Set 2: {len(question_ids)}")
        print("Note: Remaining sets need questions to be added similarly")
        
    except Exception as error:
        print(f"Error: {error}")
    finally:
        await client.close()

if __name__ == "__main__":
    asyncio.run(complete_theme1_remaining_sets())
