import asyncio
from datetime import datetime, timezone
from pymongo import AsyncMongoClient
from bson import ObjectId

# Database configuration
MONGO_URI = "mongodb+srv://diwas:<EMAIL>/"
DB_NAME = "test_nepali_app"
USER_ID = ObjectId('68391d86b8b0e7ec9ababfbb')

async def insert_theme1_educational_tours():
    """Insert Theme 1: Educational Tours with 5 curated sets and questions"""
    
    client = AsyncMongoClient(MONGO_URI)
    
    try:
        db = client[DB_NAME]
        
        # Insert Theme 1: Educational Tours
        theme = {
            "name": "शैक्षिक भ्रमण",
            "name_en": "Educational Tours", 
            "description": "विभिन्न शैक्षिक स्थानहरूको भ्रमण र सिकाइ",
            "description_en": "Educational visits and learning at various academic sites",
            "order": 1,
            "is_active": True,
            "created_at": datetime.now(timezone.utc),
            "updated_at": datetime.now(timezone.utc),
            "category": "इतिहास",
            "category_en": "History",
            "color": "#8B4513",
            "icon": "🎓"
        }
        
        theme_result = await db.themes.insert_one(theme)
        theme_id = theme_result.inserted_id
        print(f"Theme inserted with ID: {theme_id}")
        
        # Prepare 5 Curated Content Sets (following task_sets structure)
        curated_sets = []
        
        # Set 1: Darjeeling School Tour
        set1 = {
            "user_id": str(USER_ID),
            "input_type": "text",
            "input_content": "दार्जिलिङ स्कूल भ्रमण - म दिवास हुँ, रौतहट जिल्लाको गरुडा माध्यमिक विद्यालयमा कक्षा ९ मा पढ्छु। हाम्रो स्कूलले पहिलो पटक दार्जिलिङको शैक्षिक भ्रमणको आयोजना गर्यो। बिहान ५ बजे स्कूलबाट बस छुट्यो। हामी ३५ जना विद्यार्थी र ३ जना शिक्षक थियौं। पहिले हामी काकडभिट्टा पुग्यौं, त्यहाँ भारतीय सीमा पार गर्यौं। सिलिगुडी हुँदै दार्जिलिङ पुग्न ६ घण्टा लाग्यो। पहाडी बाटोमा घुम्ती सडकहरू देखेर धेरै रोमाञ्चक लाग्यो। दार्जिलिङ पुगेपछि हामीले हिमालयन माउन्टेनियरिङ इन्स्टिच्युट भ्रमण गर्यौं। त्यहाँ तेन्जिङ नोर्गे र एडमन्ड हिलारीको बारेमा जान्यौं। भोलिपल्ट बिहान टाइगर हिलबाट कञ्चनजङ्घाको सूर्योदय हेर्यौं। त्यो दृश्य अविस्मरणीय थियो। दार्जिलिङको चिया बगानमा पनि गयौं र चिया बनाउने प्रक्रिया देख्यौं।",
            "input_data": None,
            "tasks": [],  # Will be populated with task item IDs
            "created_at": datetime.now(timezone.utc),
            "submitted_at": None,
            "verified_at": None,
            "completed_at": None,
            "status": "pending",
            "difficulty": "medium",
            "total_tasks": 6,
            "attempted_tasks": 0,
            "total_score": 60,  # 6 questions × 10 points each
            "scored": 0,
            "notes": None,
            "remark": None,
            "metadata": {
                "theme_id": str(theme_id),
                "title": "दार्जिलिङ स्कूल भ्रमण",
                "title_en": "Darjeeling School Tour",
                "script_type": "educational_tour",
                "location": "दार्जिलिङ",
                "generated_by": "curated_content"
            }
        }
        
        # Set 2: Kathmandu Science Museum
        set2 = {
            "user_id": str(USER_ID),
            "input_type": "text", 
            "input_content": "काठमाडौं विज्ञान संग्रहालय भ्रमण - म सरिता हुँ, धनकुटाको सरस्वती माध्यमिक विद्यालयकी कक्षा १० की विद्यार्थी। हाम्रो विज्ञान विषयको लागि काठमाडौंको राष्ट्रिय विज्ञान संग्रहालय भ्रमण गर्यौं। त्यहाँ पुगेपछि पहिले हामी भौतिक विज्ञानको खण्डमा गयौं। त्यहाँ बिजुलीको प्रयोग, चुम्बकत्व र प्रकाशका बारेमा प्रयोगहरू थिए। सबैभन्दा रोचक कुरा के थियो भने त्यहाँ भ्यान डे ग्राफ जेनेरेटर थियो जसले स्थिर बिजुली बनाउँछ। त्यसपछि हामी रसायन विज्ञानको खण्डमा गयौं। त्यहाँ आवर्त सारणी र विभिन्न तत्वहरूका नमूनाहरू थिए। जीवविज्ञानको खण्डमा मानव शरीरका अंगहरूको मोडेल र डायनासोरका हड्डीहरू थिए। अन्तमा हामी तारामण्डलमा गयौं र ग्रह-नक्षत्रहरूको बारेमा जान्यौं।",
            "input_data": None,
            "tasks": [],
            "created_at": datetime.now(timezone.utc),
            "submitted_at": None,
            "verified_at": None,
            "completed_at": None,
            "status": "pending",
            "difficulty": "medium",
            "total_tasks": 6,
            "attempted_tasks": 0,
            "total_score": 60,
            "scored": 0,
            "notes": None,
            "remark": None,
            "metadata": {
                "theme_id": str(theme_id),
                "title": "काठमाडौं विज्ञान संग्रहालय भ्रमण",
                "title_en": "Kathmandu Science Museum Visit",
                "script_type": "educational_tour",
                "location": "काठमाडौं",
                "generated_by": "curated_content"
            }
        }
        
        # Set 3: Pokhara Mountain Museum
        set3 = {
            "user_id": str(USER_ID),
            "input_type": "text",
            "input_content": "पोखरा अन्तर्राष्ट्रिय पर्वतारोहण संग्रहालय भ्रमण - म राम हुँ, कास्कीको आदर्श माध्यमिक विद्यालयको विद्यार्थी। हाम्रो भूगोल कक्षाको लागि पोखराको अन्तर्राष्ट्रिय पर्वतारोहण संग्रहालय भ्रमण गर्यौं। यो संग्रहालय २००४ मा स्थापना भएको थियो। त्यहाँ पुगेपछि गाइडले नेपालका अष्टसहस्री पर्वतहरूको बारेमा बताए। एभरेस्ट, कञ्चनजङ्घा, ल्होत्से, मकालु, चो ओयु, धौलागिरी, मनास्लु र अन्नपूर्णा - यी सबै नेपालमा छन्। त्यहाँ पर्वतारोहीहरूले प्रयोग गर्ने उपकरणहरू थिए - रस्सी, बुट, अक्सिजन मास्क, टेन्ट। सबैभन्दा रोचक कुरा के थियो भने त्यहाँ शेर्पाहरूको संस्कृति र परम्पराको बारेमा जानकारी थियो। संग्रहालयबाट मछापुच्छ्रे र अन्नपूर्णा हिमालको सुन्दर दृश्य देखिन्थ्यो।",
            "input_data": None,
            "tasks": [],
            "created_at": datetime.now(timezone.utc),
            "submitted_at": None,
            "verified_at": None,
            "completed_at": None,
            "status": "pending",
            "difficulty": "medium",
            "total_tasks": 6,
            "attempted_tasks": 0,
            "total_score": 60,
            "scored": 0,
            "notes": None,
            "remark": None,
            "metadata": {
                "theme_id": str(theme_id),
                "title": "पोखरा अन्तर्राष्ट्रिय पर्वतारोहण संग्रहालय भ्रमण",
                "title_en": "Pokhara International Mountain Museum Visit",
                "script_type": "educational_tour",
                "location": "पोखरा",
                "generated_by": "curated_content"
            }
        }
        
        # Set 4: Janakpur Cultural Study
        set4 = {
            "user_id": str(USER_ID),
            "input_type": "text",
            "input_content": "जनकपुर जानकी मन्दिर र संस्कृति अध्ययन - म गीता हुँ, धनुषाको राम जानकी माध्यमिक विद्यालयकी विद्यार्थी। हाम्रो नेपाली साहित्य र संस्कृतिको कक्षाको लागि जनकपुरको शैक्षिक भ्रमण गर्यौं। जनकपुर सीताको जन्मस्थान हो र यहाँ रामायणको कथा जोडिएको छ। पहिले हामी जानकी मन्दिर गयौं। यो मन्दिर १९११ मा निर्माण भएको थियो र यो राजस्थानी शैलीमा बनेको छ। त्यहाँ सीता र रामको मूर्ति छ। गाइडले भन्यो कि यहाँ हरेक वर्ष विवाह पञ्चमीमा ठूलो मेला लाग्छ। त्यसपछि हामी रामसागर तालमा गयौं। यो ताल राजा जनकले बनाएका थिए। त्यहाँ धेरै मन्दिरहरू छन्। जनकपुरमा मिथिला कलाको परम्परा छ। महिलाहरूले घरका भित्ताहरूमा सुन्दर चित्रहरू कोर्छन्।",
            "input_data": None,
            "tasks": [],
            "created_at": datetime.now(timezone.utc),
            "submitted_at": None,
            "verified_at": None,
            "completed_at": None,
            "status": "pending",
            "difficulty": "medium",
            "total_tasks": 6,
            "attempted_tasks": 0,
            "total_score": 60,
            "scored": 0,
            "notes": None,
            "remark": None,
            "metadata": {
                "theme_id": str(theme_id),
                "title": "जनकपुर जानकी मन्दिर र संस्कृति अध्ययन",
                "title_en": "Janakpur Janaki Temple and Cultural Study",
                "script_type": "educational_tour",
                "location": "जनकपुर",
                "generated_by": "curated_content"
            }
        }
        
        # Set 5: Chitwan National Park
        set5 = {
            "user_id": str(USER_ID),
            "input_type": "text",
            "input_content": "चितवन राष्ट्रिय निकुञ्ज शैक्षिक भ्रमण - म अर्जुन हुँ, चितवनको शहीद स्मृति माध्यमिक विद्यालयको विद्यार्थी। हाम्रो वातावरण विज्ञानको कक्षाको लागि चितवन राष्ट्रिय निकुञ्जको भ्रमण गर्यौं। यो निकुञ्ज १९७३ मा स्थापना भएको थियो र नेपालको पहिलो राष्ट्रिय निकुञ्ज हो। त्यहाँ पुगेपछि पहिले हामी निकुञ्जको मुख्यालयमा गयौं र गाइडले एक सींगे गैंडाको बारेमा बताए। नेपालमा करिब ७०० वटा गैंडा छन् र धेरैजसो चितवनमा छन्। त्यसपछि हामी जीप सफारीमा गयौं। त्यहाँ हामीले गैंडा, हात्ती, चितुवा र धेरै चराहरू देख्यौं। राप्ती नदीमा घडियाल र मगरमच्छ देख्यौं। गाइडले भन्यो कि यो निकुञ्ज युनेस्कोको विश्व सम्पदा सूचीमा छ। त्यहाँ थारु समुदायको संस्कृति पनि छ।",
            "input_data": None,
            "tasks": [],
            "created_at": datetime.now(timezone.utc),
            "submitted_at": None,
            "verified_at": None,
            "completed_at": None,
            "status": "pending",
            "difficulty": "medium",
            "total_tasks": 6,
            "attempted_tasks": 0,
            "total_score": 60,
            "scored": 0,
            "notes": None,
            "remark": None,
            "metadata": {
                "theme_id": str(theme_id),
                "title": "चितवन राष्ट्रिय निकुञ्ज शैक्षिक भ्रमण",
                "title_en": "Chitwan National Park Educational Visit",
                "script_type": "educational_tour",
                "location": "चितवन",
                "generated_by": "curated_content"
            }
        }
        
        curated_sets = [set1, set2, set3, set4, set5]
        
        # Insert all curated content sets
        set_results = await db.curated_content_set.insert_many(curated_sets)
        set_ids = list(set_results.inserted_ids)
        print(f"Inserted {len(set_ids)} curated content sets")
        
        # Now create questions for Set 1: Darjeeling Tour
        set1_questions = []
        set1_id = set_ids[0]
        
        questions_set1 = [
            {
                "task_set_id": set1_id,
                "user_id": USER_ID,
                "type": "single_choice",
                "title": "दिवास कुन जिल्लाको विद्यार्थी हो?",
                "question": {
                    "type": "text",
                    "text": "दिवास कुन जिल्लाको विद्यार्थी हो?",
                    "translated_text": "Which district is Diwas a student from?",
                    "options": ["रौतहट", "सिरहा", "महोत्तरी", "सप्तरी"],
                    "word": None,
                    "media": [],
                    "metadata": {}
                },
                "correct_answer": {
                    "type": "single_choice",
                    "value": "रौतहट",
                    "media": [],
                    "metadata": {}
                },
                "user_answer": None,
                "status": "pending",
                "result": None,
                "submitted": False,
                "is_attempted": False,
                "attempts_count": 0,
                "total_score": 10,
                "scored": 0,
                "created_at": datetime.now(timezone.utc),
                "updated_at": datetime.now(timezone.utc),
                "metadata": {"answer_hint": "स्क्रिप्टमा उल्लेख गरिएको जिल्लाको नाम हेर्नुहोस्"}
            },
            {
                "task_set_id": set1_id,
                "user_id": USER_ID,
                "type": "single_choice",
                "title": "भ्रमणमा कति जना विद्यार्थी सहभागी थिए?",
                "question": {
                    "type": "text",
                    "text": "भ्रमणमा कति जना विद्यार्थी सहभागी थिए?",
                    "translated_text": "How many students participated in the tour?",
                    "options": ["३०", "३५", "४०", "२५"],
                    "word": None,
                    "media": [],
                    "metadata": {}
                },
                "correct_answer": {
                    "type": "single_choice",
                    "value": "३५",
                    "media": [],
                    "metadata": {}
                },
                "user_answer": None,
                "status": "pending",
                "result": None,
                "submitted": False,
                "is_attempted": False,
                "attempts_count": 0,
                "total_score": 10,
                "scored": 0,
                "created_at": datetime.now(timezone.utc),
                "updated_at": datetime.now(timezone.utc),
                "metadata": {"answer_hint": "स्क्रिप्टमा उल्लेखित विद्यार्थी संख्या हेर्नुहोस्"}
            }
        ]
        
        # Insert questions for Set 1
        question_results = await db.curated_content_items.insert_many(questions_set1)
        question_ids = list(question_results.inserted_ids)
        
        # Update Set 1 with question IDs
        await db.curated_content_set.update_one(
            {"_id": set1_id},
            {"$set": {"tasks": [str(qid) for qid in question_ids]}}
        )
        
        print(f"Successfully created Theme 1: Educational Tours")
        print(f"Theme ID: {theme_id}")
        print(f"Total Sets: {len(set_ids)}")
        print(f"Questions for Set 1: {len(question_ids)}")
        
    except Exception as error:
        print(f"Error: {error}")
    finally:
        client.close()

if __name__ == "__main__":
    asyncio.run(insert_theme1_educational_tours())
