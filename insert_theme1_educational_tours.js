const { MongoClient, ObjectId } = require('mongodb');

const uri = 'mongodb+srv://diwas:<EMAIL>/';
const dbName = 'test_nepali_app';
const userId = new ObjectId('68391d86b8b0e7ec9ababfbb');

async function insertTheme1() {
  const client = new MongoClient(uri);
  
  try {
    await client.connect();
    const db = client.db(dbName);
    
    // Insert Theme 1: Educational Tours
    const theme = {
      name: "शैक्षिक भ्रमण",
      name_en: "Educational Tours", 
      description: "विभिन्न शैक्षिक स्थानहरूको भ्रमण र सिकाइ",
      description_en: "Educational visits and learning at various academic sites",
      order: 1,
      is_active: true,
      created_at: new Date(),
      updated_at: new Date(),
      category: "इतिहास",
      category_en: "History",
      color: "#8B4513",
      icon: "🎓"
    };
    
    const themeResult = await db.collection('themes').insertOne(theme);
    const themeId = themeResult.insertedId;
    console.log('Theme inserted with ID:', themeId);
    
    // Insert 5 Curated Content Sets
    const curatedSets = [
      {
        title: "दार्जिलिङ स्कूल भ्रमण",
        title_en: "Darjeeling School Tour",
        script: "म दिवास हुँ, रौतहट जिल्लाको गरुडा माध्यमिक विद्यालयमा कक्षा ९ मा पढ्छु। हाम्रो स्कूलले पहिलो पटक दार्जिलिङको शैक्षिक भ्रमणको आयोजना गर्यो। बिहान ५ बजे स्कूलबाट बस छुट्यो। हामी ३५ जना विद्यार्थी र ३ जना शिक्षक थियौं। पहिले हामी काकडभिट्टा पुग्यौं, त्यहाँ भारतीय सीमा पार गर्यौं। सिलिगुडी हुँदै दार्जिलिङ पुग्न ६ घण्टा लाग्यो। पहाडी बाटोमा घुम्ती सडकहरू देखेर धेरै रोमाञ्चक लाग्यो। दार्जिलिङ पुगेपछि हामीले हिमालयन माउन्टेनियरिङ इन्स्टिच्युट भ्रमण गर्यौं। त्यहाँ तेन्जिङ नोर्गे र एडमन्ड हिलारीको बारेमा जान्यौं। भोलिपल्ट बिहान टाइगर हिलबाट कञ्चनजङ्घाको सूर्योदय हेर्यौं। त्यो दृश्य अविस्मरणीय थियो। दार्जिलिङको चिया बगानमा पनि गयौं र चिया बनाउने प्रक्रिया देख्यौं।",
        theme_id: themeId,
        user_id: userId,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        title: "काठमाडौं विज्ञान संग्रहालय भ्रमण", 
        title_en: "Kathmandu Science Museum Visit",
        script: "म सरिता हुँ, धनकुटाको सरस्वती माध्यमिक विद्यालयकी कक्षा १० की विद्यार्थी। हाम्रो विज्ञान विषयको लागि काठमाडौंको राष्ट्रिय विज्ञान संग्रहालय भ्रमण गर्यौं। त्यहाँ पुगेपछि पहिले हामी भौतिक विज्ञानको खण्डमा गयौं। त्यहाँ बिजुलीको प्रयोग, चुम्बकत्व र प्रकाशका बारेमा प्रयोगहरू थिए। सबैभन्दा रोचक कुरा के थियो भने त्यहाँ भ्यान डे ग्राफ जेनेरेटर थियो जसले स्थिर बिजुली बनाउँछ। त्यसपछि हामी रसायन विज्ञानको खण्डमा गयौं। त्यहाँ आवर्त सारणी र विभिन्न तत्वहरूका नमूनाहरू थिए। जीवविज्ञानको खण्डमा मानव शरीरका अंगहरूको मोडेल र डायनासोरका हड्डीहरू थिए। अन्तमा हामी तारामण्डलमा गयौं र ग्रह-नक्षत्रहरूको बारेमा जान्यौं।",
        theme_id: themeId,
        user_id: userId,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        title: "पोखरा अन्तर्राष्ट्रिय पर्वतारोहण संग्रहालय भ्रमण",
        title_en: "Pokhara International Mountain Museum Visit", 
        script: "म राम हुँ, कास्कीको आदर्श माध्यमिक विद्यालयको विद्यार्थी। हाम्रो भूगोल कक्षाको लागि पोखराको अन्तर्राष्ट्रिय पर्वतारोहण संग्रहालय भ्रमण गर्यौं। यो संग्रहालय २००४ मा स्थापना भएको थियो। त्यहाँ पुगेपछि गाइडले नेपालका अष्टसहस्री पर्वतहरूको बारेमा बताए। एभरेस्ट, कञ्चनजङ्घा, ल्होत्से, मकालु, चो ओयु, धौलागिरी, मनास्लु र अन्नपूर्णा - यी सबै नेपालमा छन्। त्यहाँ पर्वतारोहीहरूले प्रयोग गर्ने उपकरणहरू थिए - रस्सी, बुट, अक्सिजन मास्क, टेन्ट। सबैभन्दा रोचक कुरा के थियो भने त्यहाँ शेर्पाहरूको संस्कृति र परम्पराको बारेमा जानकारी थियो। संग्रहालयबाट मछापुच्छ्रे र अन्नपूर्णा हिमालको सुन्दर दृश्य देखिन्थ्यो।",
        theme_id: themeId,
        user_id: userId,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        title: "जनकपुर जानकी मन्दिर र संस्कृति अध्ययन",
        title_en: "Janakpur Janaki Temple and Cultural Study",
        script: "म गीता हुँ, धनुषाको राम जानकी माध्यमिक विद्यालयकी विद्यार्थी। हाम्रो नेपाली साहित्य र संस्कृतिको कक्षाको लागि जनकपुरको शैक्षिक भ्रमण गर्यौं। जनकपुर सीताको जन्मस्थान हो र यहाँ रामायणको कथा जोडिएको छ। पहिले हामी जानकी मन्दिर गयौं। यो मन्दिर १९११ मा निर्माण भएको थियो र यो राजस्थानी शैलीमा बनेको छ। त्यहाँ सीता र रामको मूर्ति छ। गाइडले भन्यो कि यहाँ हरेक वर्ष विवाह पञ्चमीमा ठूलो मेला लाग्छ। त्यसपछि हामी रामसागर तालमा गयौं। यो ताल राजा जनकले बनाएका थिए। त्यहाँ धेरै मन्दिरहरू छन्। जनकपुरमा मिथिला कलाको परम्परा छ। महिलाहरूले घरका भित्ताहरूमा सुन्दर चित्रहरू कोर्छन्।",
        theme_id: themeId,
        user_id: userId,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        title: "चितवन राष्ट्रिय निकुञ्ज शैक्षिक भ्रमण",
        title_en: "Chitwan National Park Educational Visit",
        script: "म अर्जुन हुँ, चितवनको शहीद स्मृति माध्यमिक विद्यालयको विद्यार्थी। हाम्रो वातावरण विज्ञानको कक्षाको लागि चितवन राष्ट्रिय निकुञ्जको भ्रमण गर्यौं। यो निकुञ्ज १९७३ मा स्थापना भएको थियो र नेपालको पहिलो राष्ट्रिय निकुञ्ज हो। त्यहाँ पुगेपछि पहिले हामी निकुञ्जको मुख्यालयमा गयौं र गाइडले एक सींगे गैंडाको बारेमा बताए। नेपालमा करिब ७०० वटा गैंडा छन् र धेरैजसो चितवनमा छन्। त्यसपछि हामी जीप सफारीमा गयौं। त्यहाँ हामीले गैंडा, हात्ती, चितुवा र धेरै चराहरू देख्यौं। राप्ती नदीमा घडियाल र मगरमच्छ देख्यौं। गाइडले भन्यो कि यो निकुञ्ज युनेस्कोको विश्व सम्पदा सूचीमा छ। त्यहाँ थारु समुदायको संस्कृति पनि छ।",
        theme_id: themeId,
        user_id: userId,
        created_at: new Date(),
        updated_at: new Date()
      }
    ];
    
    const setResults = await db.collection('curated_content_set').insertMany(curatedSets);
    console.log('Inserted curated content sets:', Object.keys(setResults.insertedIds).length);
    
    // Insert questions for each set
    const allQuestions = [];
    
    // Questions for Set 1: Darjeeling Tour
    const set1Id = setResults.insertedIds[0];
    allQuestions.push(
      {
        type: "single_choice",
        title: "दिवास कुन जिल्लाको विद्यार्थी हो?",
        question: {
          type: "text",
          text: "दिवास कुन जिल्लाको विद्यार्थी हो?",
          translated_text: "Which district is Diwas a student from?",
          options: ["रौतहट", "सिरहा", "महोत्तरी", "सप्तरी"],
          answer_hint: "स्क्रिप्टमा उल्लेख गरिएको जिल्लाको नाम हेर्नुहोस्"
        },
        correct_answer: {
          type: "single",
          value: "रौतहट"
        },
        task_set_id: set1Id,
        user_id: userId,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        type: "single_choice", 
        title: "भ्रमणमा कति जना विद्यार्थी सहभागी थिए?",
        question: {
          type: "text",
          text: "भ्रमणमा कति जना विद्यार्थी सहभागी थिए?",
          translated_text: "How many students participated in the tour?",
          options: ["३०", "३५", "४०", "२५"],
          answer_hint: "स्क्रिप्टमा उल्लेखित विद्यार्थी संख्या हेर्नुहोस्"
        },
        correct_answer: {
          type: "single",
          value: "३५"
        },
        task_set_id: set1Id,
        user_id: userId,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        type: "single_choice",
        title: "दार्जिलिङ पुग्न कति घण्टा लाग्यो?",
        question: {
          type: "text", 
          text: "दार्जिलिङ पुग्न कति घण्टा लाग्यो?",
          translated_text: "How many hours did it take to reach Darjeeling?",
          options: ["५ घण्टा", "६ घण्टा", "७ घण्टा", "८ घण्टा"],
          answer_hint: "यात्राको समयावधि स्क्रिप्टमा उल्लेख छ"
        },
        correct_answer: {
          type: "single",
          value: "६ घण्टा"
        },
        task_set_id: set1Id,
        user_id: userId,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        type: "multiple_choice",
        title: "हिमालयन माउन्टेनियरिङ इन्स्टिच्युटमा कसका बारेमा जानकारी पाए?",
        question: {
          type: "text",
          text: "हिमालयन माउन्टेनियरिङ इन्स्टिच्युटमा कसका बारेमा जानकारी पाए?",
          translated_text: "Who did they learn about at the Himalayan Mountaineering Institute?",
          options: ["तेन्जिङ नोर्गे", "एडमन्ड हिलारी", "जुङ्गो ताबेई", "रेनहोल्ड मेसनर"],
          answer_hint: "स्क्रिप्टमा उल्लेखित दुई व्यक्तित्व छान्नुहोस्"
        },
        correct_answer: {
          type: "multiple",
          value: ["तेन्जिङ नोर्गे", "एडमन्ड हिलारी"]
        },
        task_set_id: set1Id,
        user_id: userId,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        type: "single_choice",
        title: "कुन ठाउँबाट कञ्चनजङ्घाको सूर्योदय हेरे?",
        question: {
          type: "text",
          text: "कुन ठाउँबाट कञ्चनजङ्घाको सूर्योदय हेरे?",
          translated_text: "From which place did they watch the sunrise over Kanchenjunga?",
          options: ["टाइगर हिल", "डार्जिलिङ मल", "हिमालयन इन्स्टिच्युट", "बाटासिया लुप"],
          answer_hint: "सूर्योदय हेर्ने प्रसिद्ध स्थान"
        },
        correct_answer: {
          type: "single",
          value: "टाइगर हिल"
        },
        task_set_id: set1Id,
        user_id: userId,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        type: "single_choice",
        title: "भारतीय सीमा कुन ठाउँमा पार गरे?",
        question: {
          type: "text",
          text: "भारतीय सीमा कुन ठाउँमा पार गरे?",
          translated_text: "Where did they cross the Indian border?",
          options: ["काकडभिट्टा", "बिर्गञ्ज", "भैरहवा", "महेन्द्रनगर"],
          answer_hint: "पूर्वी सीमानाका प्रमुख नाका"
        },
        correct_answer: {
          type: "single",
          value: "काकडभिट्टा"
        },
        task_set_id: set1Id,
        user_id: userId,
        created_at: new Date(),
        updated_at: new Date()
      }
    );
    
    const questionResults = await db.collection('curated_content_items').insertMany(allQuestions);
    console.log('Inserted questions for Set 1:', questionResults.insertedIds.length);
    
    console.log('Successfully created Theme 1: Educational Tours');
    console.log('Theme ID:', themeId);
    console.log('Total Sets:', Object.keys(setResults.insertedIds).length);
    console.log('Total Questions:', questionResults.insertedIds.length);
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await client.close();
  }
}

insertTheme1();
